
import csv
import re
import os

# --- Configuration ---
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report.txt')
output_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report_cleaned_v2.csv')

# Regex to find potential song numbers (alphanumeric, at least 6 chars, may start with a quote)
SONG_ID_REGEX = r"['a-zA-Z0-9]{6,}"


def find_representative_song_id(row_data):
    # Combine the relevant columns into a single string for searching
    # Columns are: 备注 (6), 相同 (7), 不相同 (8)
    # We check if the row has enough columns before calling this function
    search_string = ' '.join(row_data[6:9])
    
    matches = re.findall(SONG_ID_REGEX, search_string)
    
    return matches[0] if matches else ''

# --- Main Execution ---

# Ensure the output directory exists
os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

processed_rows = []
song_count = 0

with open(input_file_path, 'r', encoding='utf-8') as f_in:
    lines = f_in.readlines()

# Find the header row (the first non-sheet, non-empty line)
header = None
start_index = 0
for i, line in enumerate(lines):
    if line.strip() and not line.strip().startswith('---'):
        header = [h.strip() for h in line.split(',')]
        header.append('统一歌号')
        processed_rows.append(header)
        start_index = i + 1
        break

if header:
    # Process lines after the header
    for line in lines[start_index:]:
        line = line.strip()
        # Skip empty lines and sheet separators
        if not line or line.startswith('---'):
            continue

        row = [cell.strip() for cell in next(csv.reader([line]))]
        
        # This is a song data row
        song_count += 1
        
        # Ensure row has enough columns to avoid index errors
        if len(row) > 8:
            song_id = find_representative_song_id(row)
            row.append(song_id)
        else:
            # Pad row if it's too short, then add empty song_id
            row.extend([''] * (9 - len(row)))
            row.append('')

        # Ensure the final row has the same number of columns as the header
        while len(row) < len(header):
            row.append('')
        
        processed_rows.append(row[:len(header)]) # Truncate if longer

# Write the cleaned data to a new CSV file
if processed_rows:
    with open(output_file_path, 'w', newline='', encoding='utf-8-sig') as f_out:
        writer = csv.writer(f_out)
        writer.writerows(processed_rows)
    print(f"清理完成，共处理 {song_count} 首歌曲。已将结果保存到: {os.path.abspath(output_file_path)}")
else:
    print("没有数据被处理。")

