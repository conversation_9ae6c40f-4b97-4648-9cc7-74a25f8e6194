# KTV 报表系统开发计划

本文档用于跟踪和规划KTV数据分析平台中所有报表的开发进度。

## 报表开发清单 (未来工作)

| 报表大类 | 报表名称 | 状态 | 负责人 | 备注 |
|---|---|---|---|---|
| 运营报表 | 客群人数分析报表 | 进行中 | Gemini | 基于 Fact_Daily_BatchSize_Summary，数据模型及创建脚本已完成 |
| 营销报表 | 营销活动数据 | 未开始 | Gemini | |
| 营销报表 | 卡券数据 | 进行中 | Gemini | 数据模型设计已完成 (Dim_Coupon, Fact_Daily_Coupon_Summary) |
| 营销报表 | 促销活动数据 | 未开始 | Gemini | |
| 会员报表 | 会员统计 | 未开始 | Gemini | |
| 会员报表 | **储值套餐汇总报表** | **已完成 (数据模型)** | Gemini | 数据模型已定义 |
| 会员报表 | 会员交易汇总 | 未开始 | Gemini | |
| 会员报表 | 权益卡统计 | 未开始 | Gemini | |
| 会员报表 | 礼品卡统计 | 未开始 | Gemini | |
| 月报 | 收入统计表 | 未开始 | Gemini | |
| 月报 | 收入明细表 | 未开始 | Gemini | |
| 月报 | 订单渠道统计表 | 未开始 | Gemini | |
| 月报 | 时段统计表 | 未开始 | Gemini | |

---

## 已完成报表技术详情 (历史参考)

### 1. 预订报表 (Booking Report)
- **核心存储过程**: `usp_GetBookingReport`
- **主要依赖数据表**: `bookhistory`, `bookcacheinfo`, `mims.dbo.shopinfo`, `mims.dbo.memberinfo`

### 2. 总的营业报表 (Overall Business Summary Report)
- **核心存储过程**: `usp_GenerateDynamicUnifiedDailyReport`
- **主要依赖数据表**: `RmCloseInfo`, `FdCashBak`, `shoptimeinfo`, `mims.dbo.shopinfo`

### 3. 银行细分报表 (Bank Segmentation Report / 历史卡券数据)
- **核心存储过程**: `sp_populate_fact_deal_redemption`
- **主要依赖数据表**: `mims.dbo.deals` (源), `Dim_Date`, `Dim_Shop`, `Dim_Bank_Deal`, `Fact_Deal_Redemption` (目标)
