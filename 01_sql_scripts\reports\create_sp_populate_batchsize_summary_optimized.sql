CREATE OR ALTER PROCEDURE usp_Populate_Fact_Daily_BatchSize_Summary
    @RunDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    -- 1. Get DateSK from the run date
    DECLARE @DateSK INT;
    SELECT @DateSK = DateSK FROM dbo.Dim_Date WHERE FullDate = @RunDate;

    IF @DateSK IS NULL
    BEGIN
        PRINT 'Warning: DateSK not found for the given date. Aborting.';
        RETURN;
    END;

    -- 2. Delete old data for the given date
    DELETE FROM dbo.Fact_Daily_BatchSize_Summary WHERE DateSK = @DateSK;

    -- 3. Use CTEs to process data
    WITH BillSourceData AS (
        SELECT
            rci.Shopid,
            rci.InvNo,
            rci.OpenDateTime,
            oci.ComeDate,
            oci.ComeTime,
            fd.FdQty AS GuestCount,
            (rci.Cash + rci.Cash_Targ * 0.8 + rci.Vesa + rci.GZ + rci.AccOkZD + rci.RechargeAccount + rci.NoPayed + rci.WXPay + rci.AliPay + rci.MTPay + rci.DZPay + rci.NMPay + rci.[Check] + rci.WechatDeposit + rci.WechatShopping + rci.ReturnAccount + ISNULL(rci.WechatOfficialPay, 0)) AS BillAmount
        FROM
            operatedata.dbo.rmcloseinfo AS rci
        LEFT JOIN
            operatedata.dbo.opencacheinfo AS oci ON rci.InvNo = oci.Invno COLLATE DATABASE_DEFAULT AND rci.Shopid = oci.ShopId
        JOIN
            operatedata.dbo.fdcashbak AS fd ON rci.InvNo = fd.InvNo COLLATE DATABASE_DEFAULT
        WHERE
            rci.WorkDate = FORMAT(@RunDate, 'yyyyMMdd')
            AND fd.FdCName = N'消费人数'
            AND fd.CashType = 'N'
    ),
    BillWithEffectiveTime AS (
        SELECT
            *,
            COALESCE(
                OpenDateTime,
                CASE
                    WHEN ComeDate IS NOT NULL AND ComeTime IS NOT NULL AND ISDATE(ComeDate + ' ' + ComeTime) = 1
                    THEN CONVERT(datetime, ComeDate + ' ' + ComeTime, 120)
                    ELSE NULL
                END
            ) AS EffectiveOpenDateTime
        FROM BillSourceData
    ),
    BillWithCategory AS (
        SELECT
            s.ShopSK,
            b.InvNo,
            b.GuestCount,
            b.BillAmount,
            CASE 
                WHEN CAST(b.EffectiveOpenDateTime AS TIME) < '20:00:00' THEN N'自助餐' 
                ELSE N'黄金档' 
            END AS Category
        FROM
            BillWithEffectiveTime b
        JOIN 
            dbo.Dim_Shop s ON b.Shopid = s.ShopID
        WHERE 
            b.EffectiveOpenDateTime IS NOT NULL -- Filter out records that still have no valid time
    )
    -- Final Insert
    INSERT INTO dbo.Fact_Daily_BatchSize_Summary (
        DateSK, ShopSK, Category, TotalBatches, TotalAmount,
        Batches_2_Person, Amount_2_Person, Batches_3_Person, Amount_3_Person,
        Batches_4_Person, Amount_4_Person, Batches_5_Person, Amount_5_Person,
        Batches_6_Person, Amount_6_Person, Batches_7_Person, Amount_7_Person,
        Batches_8_Person, Amount_8_Person, Batches_9_Person, Amount_9_Person,
        Batches_10_Plus_Person, Amount_10_Plus_Person
    )
    SELECT
        @DateSK,
        ShopSK,
        Category,
        COUNT(InvNo),
        SUM(BillAmount),
        COUNT(CASE WHEN GuestCount = 2 THEN 1 END), SUM(CASE WHEN GuestCount = 2 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 3 THEN 1 END), SUM(CASE WHEN GuestCount = 3 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 4 THEN 1 END), SUM(CASE WHEN GuestCount = 4 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 5 THEN 1 END), SUM(CASE WHEN GuestCount = 5 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 6 THEN 1 END), SUM(CASE WHEN GuestCount = 6 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 7 THEN 1 END), SUM(CASE WHEN GuestCount = 7 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 8 THEN 1 END), SUM(CASE WHEN GuestCount = 8 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 9 THEN 1 END), SUM(CASE WHEN GuestCount = 9 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount >= 10 THEN 1 END), SUM(CASE WHEN GuestCount >= 10 THEN BillAmount ELSE 0 END)
    FROM
        BillWithCategory
    GROUP BY
        ShopSK,
        Category;

    SET NOCOUNT OFF;
END;
GO