import pandas as pd
import os

# 专门读取储值套餐统计表
file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\储值套餐统计表.xlsx'

try:
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
    else:
        print(f"正在读取文件: {file_path}")
        
        # 尝试不同的引擎和参数
        engines = ['openpyxl', 'xlrd']
        
        for engine in engines:
            try:
                print(f"尝试使用引擎: {engine}")
                # 读取Excel文件的所有工作表
                excel_file = pd.ExcelFile(file_path, engine=engine)
                print(f"工作表名称: {excel_file.sheet_names}")
                
                # 读取每个工作表的内容
                for sheet_name in excel_file.sheet_names:
                    print(f"\n--- 工作表: {sheet_name} ---")
                    df = pd.read_excel(file_path, sheet_name=sheet_name, engine=engine)
                    print(f"行列数: {df.shape}")
                    print("前10行数据:")
                    print(df.head(10))
                    print("\n列名:")
                    print(list(df.columns))
                
                # 如果成功读取，跳出循环
                break
                
            except Exception as e:
                print(f"使用引擎 {engine} 读取失败: {str(e)}")
                
except Exception as e:
    print(f"读取文件时出错: {str(e)}")