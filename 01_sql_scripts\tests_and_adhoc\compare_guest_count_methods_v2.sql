-- Declare the target date for analysis
DECLARE @TargetDate DATE = '2025-09-03';

-- Define the business day range
DECLARE @WorkDate_varchar VARCHAR(8) = CONVERT(varchar(8), @TargetDate, 112);

-- Method A (Corrected): Fuzzy logic using SUM as per user's instruction
WITH FuzzyGuestCounts AS (
    SELECT 
        rci.Shopid,
        SUM(b.GuestCount) AS TotalFuzzyGuests
    FROM 
        operatedata.dbo.rmcloseinfo AS rci
    INNER JOIN (
        -- This subquery calculates guest count per bill using SUM
        SELECT
            InvNo, 
            ShopId, 
            SUM(ISNULL(fd.FdQty, 0)) as GuestCount -- CORRECTED from AVG to SUM
        FROM operatedata.dbo.fdcashbak fd
        WHERE 
            fd.FdCName LIKE N'%消费人数%' 
            AND fd.CashType = 'N'
        GROUP BY InvNo, ShopId
    ) AS b ON rci.InvNo = b.InvNo AND rci.Shopid = b.ShopId
    WHERE
        rci.WorkDate = @WorkDate_varchar
    GROUP BY
        rci.Shopid
),
-- Method B: Direct SUM from rmcloseinfo
DirectGuestCounts AS (
    SELECT
        Shopid,
        SUM(ISNULL(Numbers, 0)) AS TotalDirectGuests
    FROM
        operatedata.dbo.rmcloseinfo
    WHERE
        WorkDate = @WorkDate_varchar
    GROUP BY
        Shopid
)
-- Final Comparison
SELECT
    ds.ShopID,
    ds.ShopName,
    ISNULL(fgc.TotalFuzzyGuests, 0) AS GuestCount_FuzzySumMethod,
    ISNULL(dgc.TotalDirectGuests, 0) AS GuestCount_DirectMethod,
    ISNULL(dgc.TotalDirectGuests, 0) - ISNULL(fgc.TotalFuzzyGuests, 0) AS Difference_Direct_minus_Fuzzy
FROM
    dbo.Dim_Shop ds
LEFT JOIN
    FuzzyGuestCounts fgc ON ds.ShopID = fgc.Shopid
LEFT JOIN
    DirectGuestCounts dgc ON ds.ShopID = dgc.Shopid
WHERE
    ds.IsActive = 1
    AND (ISNULL(fgc.TotalFuzzyGuests, 0) > 0 OR ISNULL(dgc.TotalDirectGuests, 0) > 0) -- Only show shops with data
ORDER BY
    Difference_Direct_minus_Fuzzy DESC, ds.ShopID;