
-- ===================================================================
-- 脚本: optimize_sync_open_data_procedure_v2.sql
-- 功能: 优化 usp_Sync_RMS_DailyOpenData 存储过程的日期过滤性能 (修复变量声明问题)
-- ===================================================================

USE operatedata;
GO

ALTER PROCEDURE usp_Sync_RMS_DailyOpenData
    @SyncDate DATE = NULL
AS
BEGIN
    -- 声明所有变量
    DECLARE @TargetBusinessDate DATE;
    DECLARE @StartDateStr NVARCHAR(8);
    DECLARE @EndDateStr NVARCHAR(8);

    SET NOCOUNT ON;

    -- 赋值 @TargetBusinessDate
    IF @SyncDate IS NULL
    BEGIN
        SET @TargetBusinessDate = CAST(DATEADD(hour, -9, GETDATE() - 1) AS DATE);
    END
    ELSE
    BEGIN
        SET @TargetBusinessDate = @SyncDate;
    END

    -- 赋值字符串日期变量
    SET @StartDateStr = CONVERT(nvarchar(8), @TargetBusinessDate, 112); -- YYYYMMDD
    SET @EndDateStr = CONVERT(nvarchar(8), DATEADD(day, 1, @TargetBusinessDate), 112); -- YYYYMMDD

    BEGIN TRY
        -- 同步 cloudRms2019.rms2019.dbo.opencacheinfo
        MERGE INTO operatedata.dbo.opencacheinfo AS T
        USING (
            SELECT * 
            FROM cloudRms2019.rms2019.dbo.opencacheinfo 
            WHERE ComeDate >= @StartDateStr AND ComeDate < @EndDateStr
        ) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN UPDATE SET 
            T.CheckinStatus = S.CheckinStatus, 
            T.Invno = S.Invno, 
            T.RmNo = S.RmNo
        WHEN NOT MATCHED BY TARGET THEN INSERT 
            (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark) 
        VALUES
            (S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark);

        -- 同步 cloudRms2019.rms2019.dbo.openhistory
        MERGE INTO operatedata.dbo.opencacheinfo AS T
        USING (
            SELECT * 
            FROM cloudRms2019.rms2019.dbo.openhistory 
            WHERE ComeDate >= @StartDateStr AND ComeDate < @EndDateStr
        ) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN INSERT 
            (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark) 
        VALUES
            (S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark);
            
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();

        PRINT 'Error occurred in usp_Sync_RMS_DailyOpenData:';
        PRINT 'Error Message: ' + @ErrorMessage;
        PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS VARCHAR(10));
        PRINT 'Error State: ' + CAST(@ErrorState AS VARCHAR(10));
        
        RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
