import pyodbc
import re

def get_column_comments():
    """
    Parses the design document to extract column names and comments for
    the Fact_Daily_BatchSize_Summary table.
    """
    def get_column_comments():
    """
    Parses the design document to extract column names and comments for
    the Fact_Daily_BatchSize_Summary table.
    """
    design_doc_content = '''
### 6. Fact_Daily_BatchSize_Summary (日批次人数及金额汇总事实表)

- **用途:** 存放按不同批次人数（2人, 3人...）统计的业务指标（批次数及总金额），以支持更精细化的客群及消费能力分析。
- **粒度:** 1门店 x 1日期 x 1业务场景（如自助餐、黄金档）。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| BatchSizeSummarySK            | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date。                      |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop。                      |
| Category                      | NVARCHAR(50)         | 业务场景, 如 '自助餐', '黄金档'。         |
| TotalBatches                  | INT                  | 该场景下的总批次数。                       |
| TotalAmount                   | DECIMAL(18, 2)       | 该场景下的总金额。                         |
| Batches_2_Person              | INT                  | 2人批次数。                                |
| Amount_2_Person               | DECIMAL(18, 2)       | 2人批次的总金额。                          |
| Batches_3_Person              | INT                  | 3人批次数。                                |
| Amount_3_Person               | DECIMAL(18, 2)       | 3人批次的总金额。                          |
| Batches_4_Person              | INT                  | 4人批次数。                                |
| Amount_4_Person               | DECIMAL(18, 2)       | 4人批次的总金额。                          |
| Batches_5_Person              | INT                  | 5人批次数。                                |
| Amount_5_Person               | DECIMAL(18, 2)       | 5人批次的总金额。                          |
| Batches_6_Person              | INT                  | 6人批次数。                                |
| Amount_6_Person               | DECIMAL(18, 2)       | 6人批次的总金额。                          |
| Batches_7_Person              | INT                  | 7人批次数。                                |
| Amount_7_Person               | DECIMAL(18, 2)       | 7人批次的总金额。                          |
| Batches_8_Person              | INT                  | 8人批次数。                                |
| Amount_8_Person               | DECIMAL(18, 2)       | 8人批次的总金额。                          |
| Batches_9_Person              | INT                  | 9人批次数。                                |
| Amount_9_Person               | DECIMAL(18, 2)       | 9人批次的总金额。                          |
| Batches_10_Plus_Person        | INT                  | 10人及以上批次数。                         |
| Amount_10_Plus_Person         | DECIMAL(18, 2)       | 10人及以上批次的总金额。                   |
| CreateTime                    | DATETIME             | 创建时间。                                 |
| UpdateTime                    | DATETIME             | 最后更新时间。                             |
| OperatorId                    | INT                  | 操作人ID。                                 |
'''
    
    lines = design_doc_content.split('\n')
    comments = {}
    in_table_section = False
    
    for line in lines:
        # Find the start of the correct table section
        if "### 6. Fact_Daily_BatchSize_Summary" in line:
            in_table_section = True
            continue

        if not in_table_section:
            continue

        # We are in the right section, now process the table rows
        if line.strip().startswith('|'):
            parts = [p.strip() for p in line.split('|')]
            # A valid table row should have at least 4 parts: | col | type | desc |
            # We also skip the header separator line
            if len(parts) > 3 and "---" not in parts[1]:
                column_name = parts[1]
                comment_text = parts[3]
                if column_name and comment_text and column_name != "字段名 (Column Name)":
                    comments[column_name] = comment_text
        
        # If we've started parsing a table and hit a line that's not a table row, we can stop
        elif comments: 
            break
            
    return comments
    
    # Regex to find the markdown table and extract rows
    table_regex = r"""### 6. Fact_Daily_BatchSize_Summary.*?\n\n(.*?)\n\n"""
    match = re.search(table_regex, design_doc_content, re.DOTALL)
    if not match:
        return {{}}

    table_content = match.group(1)
    rows = table_content.strip().split('\n')[2:] # Skip header and separator
    
    comments = {}
    for row in rows:
        parts = [p.strip() for p in row.split('|')]
        if len(parts) > 3:
            column_name = parts[1]
            comment_text = parts[3]
            if column_name and comment_text:
                comments[column_name] = comment_text
    return comments

def add_comments_to_db(comments):
    """
    Connects to the database and adds comments to the table columns.
    """
    conn_str = (
        "DRIVER={ODBC Driver 17 for SQL Server};"
        "SERVER=***********;"
        "DATABASE=operatedata;"
        "UID=sa;"
        "PWD=Musicbox123;"
        "TrustServerCertificate=yes;"
    )
    
    sql_template = '''
    IF EXISTS (
        SELECT 1
        FROM sys.extended_properties
        WHERE major_id = OBJECT_ID(N'[dbo].[Fact_Daily_BatchSize_Summary]')
          AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Fact_Daily_BatchSize_Summary]') AND name = '{col}')
          AND name = N'MS_Description'
    )
    EXEC sp_updateextendedproperty 
        @name = N'MS_Description', 
        @value = N'{val}', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'Fact_Daily_BatchSize_Summary',
        @level2type = N'COLUMN', @level2name = N'{col}';
    ELSE
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'{val}', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'Fact_Daily_BatchSize_Summary',
        @level2type = N'COLUMN', @level2name = N'{col}';
    '''

    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        print("Successfully connected to the database.")

        for column, comment in comments.items():
            # The comment from the doc might contain single quotes, which need to be escaped for SQL
            sanitized_comment = comment.replace("'", "''")
            sql_command = sql_template.format(col=column, val=sanitized_comment)
            
            try:
                print(f"Adding/Updating comment for column: {column}...")
                cursor.execute(sql_command)
                conn.commit()
                print(f" -> Success.")
            except pyodbc.Error as ex_inner:
                sqlstate = ex_inner.args[0]
                print(f" -> ERROR applying comment for column {column}: {sqlstate}")
                print(f"   SQL attempted: {sql_command}")
                conn.rollback()

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"Database connection error: {sqlstate}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    column_comments = get_column_comments()
    if column_comments:
        print(f"Found {len(column_comments)} comments to apply.")
        add_comments_to_db(column_comments)
    else:
        print("Could not find or parse the comments for Fact_Daily_BatchSize_Summary.")
