/*
Creates a stored procedure to refresh the Dim_Deal_Map table based on the latest data in the food table.
*/
IF OBJECT_ID('usp_Refresh_Dim_Deal_Map', 'P') IS NOT NULL
    DROP PROCEDURE usp_Refresh_Dim_Deal_Map;
GO

CREATE PROCEDURE usp_Refresh_Dim_Deal_Map
AS
BEGIN
    -- Prevent row counts from being returned for better control
    SET NOCOUNT ON;

    -- Announce the start of the process
    PRINT 'Starting refresh of Dim_Deal_Map...';

    -- Step 1: Clear the existing mapping data to ensure a fresh start
    TRUNCATE TABLE Dim_Deal_Map;
    PRINT 'Dim_Deal_Map table has been truncated.';

    -- Step 2: Insert the new mappings from the food table.
    -- This uses a Common Table Expression (CTE) to first assign a channel name string,
    -- and then joins to Dim_Channel to get the appropriate ChannelSK.
    WITH FoodWithChannel AS (
        SELECT
            ShopId,
            FdNo,
            FdCName,
            FdPrice2,
            CASE
                WHEN FdCName LIKE N'%美团%' OR FdCName LIKE N'%美预%' THEN N'美团'
                WHEN FdCName LIKE N'%抖音%' OR FdCName LIKE N'%抖预%' THEN N'抖音'
                WHEN FdCName LIKE N'%广发%' OR FdCName LIKE N'%中信%' OR FdCName LIKE N'%广日%' THEN N'银行'
                -- Add more channel rules here if needed
                ELSE NULL
            END AS ChannelName_Derived
        FROM food
    )
    INSERT INTO Dim_Deal_Map (ShopId, FdNo, ChannelSK, Source_FdName, FdPrice2, CreateTime, UpdateTime)
    SELECT
        fwc.ShopId,
        fwc.FdNo,
        dc.ChannelSK,
        fwc.FdCName,
        fwc.FdPrice2,
        GETDATE(),
        GETDATE()
    FROM FoodWithChannel fwc
    JOIN Dim_Channel dc ON fwc.ChannelName_Derived = dc.ChannelName
    WHERE fwc.ChannelName_Derived IS NOT NULL; -- Only insert rows that were successfully categorized

    -- Print the number of rows inserted
    PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows have been inserted into Dim_Deal_Map.';
    PRINT 'Refresh of Dim_Deal_Map completed successfully.';

END;
GO