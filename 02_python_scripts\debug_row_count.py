import os

script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report.txt')

song_count = 0
with open(input_file_path, 'r', encoding='utf-8') as f_in:
    lines = f_in.readlines()

for i, line in enumerate(lines):
    line = line.strip()
    if not line or line.startswith('---') or '歌曲名称' in line:
        continue
    
    song_count += 1
    print(f"Row {song_count}: {line}")

print(f"\nTotal songs counted: {song_count}")
