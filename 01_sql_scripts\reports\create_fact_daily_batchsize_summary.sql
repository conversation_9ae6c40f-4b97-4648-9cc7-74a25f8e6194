-- Recreating Fact_Daily_BatchSize_Summary with the correct structure from the design document
CREATE TABLE Fact_Daily_BatchSize_Summary (
    BatchSizeSummarySK BIGINT PRIMARY KEY IDENTITY(1,1),
    DateSK INT,
    ShopSK INT,
    Category NVARCHAR(50), -- 例如: '自助餐', '黄金档'
    TotalBatches INT,
    TotalAmount DECIMAL(18, 2),
    Batches_2_Person INT,
    Amount_2_Person DECIMAL(18, 2),
    Batches_3_Person INT,
    Amount_3_Person DECIMAL(18, 2),
    Batches_4_Person INT,
    Amount_4_Person DECIMAL(18, 2),
    Batches_5_Person INT,
    Amount_5_Person DECIMAL(18, 2),
    Batches_6_Person INT,
    Amount_6_Person DECIMAL(18, 2),
    Batches_7_Person INT,
    Amount_7_Person DECIMAL(18, 2),
    Batches_8_Person INT,
    Amount_8_Person DECIMAL(18, 2),
    Batches_9_Person INT,
    Amount_9_Person DECIMAL(18, 2),
    Batches_10_Plus_Person INT,
    Amount_10_Plus_Person DECIMAL(18, 2),
    CreateTime DATETIME DEFAULT GETDATE(),
    UpdateTime DATETIME DEFAULT GETDATE(),
    OperatorId INT
);
