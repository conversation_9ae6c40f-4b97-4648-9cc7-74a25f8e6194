
USE operatedata;
GO

-- Create Fact_BankDeal_Transaction table
IF OBJECT_ID('dbo.Fact_BankDeal_Transaction', 'U') IS NULL
BEGIN
    CREATE TABLE dbo.Fact_BankDeal_Transaction (
        TransactionSK BIGINT PRIMARY KEY IDENTITY(1,1),
        DateSK INT NOT NULL, -- 关联Dim_Date
        ShopSK INT NOT NULL, -- 关联Dim_Shop
        DealType NVARCHAR(50) NOT NULL, -- 交易类型, 如 '广发银行', '中信银行', '美团'
        TransactionAmount DECIMAL(18, 2) NOT NULL, -- 交易金额
        SourceTransactionId NVARCHAR(100), -- 源系统交易号，用于追溯
        TransactionTime DATETIME NOT NULL, -- 交易发生时间
        IsReconciled BIT DEFAULT 0, -- 是否已对账
        CreateTime DATETIME DEFAULT GETDATE()
    );
    PRINT 'Table Fact_BankDeal_Transaction created successfully.';
END
ELSE
BEGIN
    PRINT 'Table Fact_BankDeal_Transaction already exists.';
END
GO

-- Create Fact_Daily_BankDeal_Summary table
IF OBJECT_ID('dbo.Fact_Daily_BankDeal_Summary', 'U') IS NULL
BEGIN
    CREATE TABLE dbo.Fact_Daily_BankDeal_Summary (
        SummarySK BIGINT PRIMARY KEY IDENTITY(1,1),
        DateSK INT NOT NULL, -- 关联Dim_Date
        ShopSK INT NOT NULL, -- 关联Dim_Shop
        TotalAmount DECIMAL(18, 2) NOT NULL, -- 当日总交易额
        MeituanAmount DECIMAL(18, 2) NOT NULL, -- 当日新美大交易额
        MeituanRatio AS (CASE WHEN TotalAmount = 0 THEN 0 ELSE MeituanAmount / TotalAmount END), -- 新美大占比 (计算列)
        GfBankAmount DECIMAL(18, 2) DEFAULT 0, -- 广发银行交易额
        CiticBankAmount DECIMAL(18, 2) DEFAULT 0, -- 中信银行交易额
        OtherBankAmount DECIMAL(18, 2) DEFAULT 0, -- 其他银行交易额
        CreateTime DATETIME DEFAULT GETDATE(),
        UpdateTime DATETIME DEFAULT GETDATE()
    );
    PRINT 'Table Fact_Daily_BankDeal_Summary created successfully.';
END
ELSE
BEGIN
    PRINT 'Table Fact_Daily_BankDeal_Summary already exists.';
END
GO
