
IF OBJECT_ID('dbo.usp_Refresh_Dim_Deal_Map', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Refresh_Dim_Deal_Map;
END
GO

CREATE PROCEDURE dbo.usp_Refresh_Dim_Deal_Map
WITH RECOMPILE -- Force a new execution plan every time
AS
BEGIN
    SET NOCOUNT ON;

    PRINT 'Starting refresh of Dim_Deal_Map from operatedata.dbo.food with RECOMPILE option.';

    TRUNCATE TABLE Dim_Deal_Map;
    PRINT 'Dim_Deal_Map table has been truncated.';

    WITH DealsWithSubChannel AS (
        SELECT
            f.ShopId,
            f.FdNo,
            f.FdCName,
            f.FdPrice2,
            CASE
                WHEN f.FdCName LIKE N'%广发%' THEN N'广发银行'
                WHEN f.FdCName LIKE N'%中信%' THEN N'中信银行'
                WHEN f.FdCName LIKE N'%广日%' THEN N'广日银联'
                WHEN f.FdCName LIKE N'%美预%' THEN N'美预'
                WHEN f.FdCName LIKE N'%美团%' THEN N'美团'
                WHEN f.FdCName LIKE N'%抖预%' THEN N'抖预'
                WHEN f.FdCName LIKE N'%抖音%' THEN N'抖音'
                ELSE NULL
            END AS SubChannelName_Derived
        FROM operatedata.dbo.food AS f
    )
    INSERT INTO Dim_Deal_Map (ShopId, FdNo, ChannelSK, Source_FdName, FdPrice2, CreateTime, UpdateTime)
    SELECT
        dws.ShopId,
        dws.FdNo,
        dc.ChannelSK,
        dws.FdCName,
        dws.FdPrice2,
        GETDATE(),
        GETDATE()
    FROM DealsWithSubChannel dws
    JOIN Dim_Channel dc ON dws.SubChannelName_Derived = dc.SubChannelName
    WHERE dws.SubChannelName_Derived IS NOT NULL;

    PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows have been inserted into Dim_Deal_Map.';
    PRINT 'Refresh of Dim_Deal_Map completed successfully.';

END;
GO
