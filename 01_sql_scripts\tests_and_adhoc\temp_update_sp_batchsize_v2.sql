ALTER PROCEDURE dbo.usp_Populate_Fact_Daily_BatchSize_Summary
    @RunDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @DateSK INT;
    SELECT @DateSK = DateSK FROM dbo.Dim_Date WHERE FullDate = @RunDate;

    IF @DateSK IS NULL
    BEGIN
        PRINT 'Warning: DateSK not found for the given date. Aborting.';
        RETURN;
    END

    DELETE FROM dbo.Fact_Daily_BatchSize_Summary WHERE DateSK = @DateSK;

    WITH DistinctBillSource AS (
        SELECT DISTINCT
            rci.InvNo,
            rci.Shopid,
            rci.OpenDateTime,
            oci.ComeDate,
            oci.ComeTime,
            (rci.Cash + rci.Cash_Targ * 0.8 + rci.Vesa + rci.GZ + rci.AccOkZD + rci.RechargeAccount + rci.NoPayed + rci.WXPay + rci.AliPay + rci.MTPay + rci.DZPay + rci.NMPay + rci.[Check] + rci.WechatDeposit + rci.WechatShopping + rci.ReturnAccount + ISNULL(rci.WechatOfficialPay, 0)) AS BillAmount
        FROM
            operatedata.dbo.rmcloseinfo AS rci
        JOIN
            operatedata.dbo.fdcashbak AS fd ON rci.InvNo = fd.InvNo
        LEFT JOIN
            operatedata.dbo.opencacheinfo AS oci ON rci.InvNo = oci.Invno COLLATE DATABASE_DEFAULT AND rci.Shopid = oci.ShopId
        WHERE
            rci.WorkDate = CONVERT(varchar(8), @RunDate, 112)
            AND fd.FdCName LIKE N'%消费人数%'
            AND fd.CashType = 'N'
    ),
    BillGuestCounts AS (
        -- CORRECTED LOGIC: Use SUM instead of AVG as per business rules.
        SELECT
            InvNo, ShopId, SUM(ISNULL(fd.FdQty, 0)) as GuestCount
        FROM operatedata.dbo.fdcashbak fd
        WHERE fd.FdCName LIKE N'%消费人数%' AND fd.CashType = 'N'
        GROUP BY InvNo, ShopId
    ),
    BillWithEffectiveTime AS (
        SELECT
            dbs.*,
            bgc.GuestCount,
            COALESCE(
                dbs.OpenDateTime,
                CASE
                    WHEN dbs.ComeDate IS NOT NULL AND dbs.ComeTime IS NOT NULL AND ISDATE(dbs.ComeDate + ' ' + dbs.ComeTime) = 1
                    THEN CONVERT(datetime, dbs.ComeDate + ' ' + dbs.ComeTime, 120)
                    ELSE NULL
                END
            ) AS EffectiveOpenDateTime
        FROM DistinctBillSource dbs
        JOIN BillGuestCounts bgc ON dbs.InvNo = bgc.InvNo AND dbs.Shopid = bgc.ShopId
    ),
    BillWithCategory AS (
        SELECT
            s.ShopSK,
            b.InvNo,
            b.GuestCount,
            b.BillAmount,
            CASE
                WHEN CAST(b.EffectiveOpenDateTime AS TIME) < '20:00:00' THEN N'自助餐'
                ELSE N'黄金档'
            END AS Category
        FROM
            BillWithEffectiveTime b
        JOIN
            dbo.Dim_Shop s ON b.Shopid = s.ShopID
        WHERE
            b.EffectiveOpenDateTime IS NOT NULL
    )
    INSERT INTO dbo.Fact_Daily_BatchSize_Summary (
        DateSK, ShopSK, Category, TotalBatches, TotalAmount,
        Batches_2_Person, Amount_2_Person, Batches_3_Person, Amount_3_Person,
        Batches_4_Person, Amount_4_Person, Batches_5_Person, Amount_5_Person,
        Batches_6_Person, Amount_6_Person, Batches_7_Person, Amount_7_Person,
        Batches_8_Person, Amount_8_Person, Batches_9_Person, Amount_9_Person,
        Batches_10_Plus_Person, Amount_10_Plus_Person
    )
    SELECT
        @DateSK,
        ShopSK,
        Category,
        COUNT(InvNo),
        SUM(BillAmount),
        COUNT(CASE WHEN GuestCount = 2 THEN 1 END), SUM(CASE WHEN GuestCount = 2 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 3 THEN 1 END), SUM(CASE WHEN GuestCount = 3 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 4 THEN 1 END), SUM(CASE WHEN GuestCount = 4 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 5 THEN 1 END), SUM(CASE WHEN GuestCount = 5 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 6 THEN 1 END), SUM(CASE WHEN GuestCount = 6 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 7 THEN 1 END), SUM(CASE WHEN GuestCount = 7 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 8 THEN 1 END), SUM(CASE WHEN GuestCount = 8 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount = 9 THEN 1 END), SUM(CASE WHEN GuestCount = 9 THEN BillAmount ELSE 0 END),
        COUNT(CASE WHEN GuestCount >= 10 THEN 1 END), SUM(CASE WHEN GuestCount >= 10 THEN BillAmount ELSE 0 END)
    FROM
        BillWithCategory
    GROUP BY
        ShopSK,
        Category;
END
