SELECT
    SUM(CASE            WHEN b.cashtype = 'N' THEN f.fdprice2 * b.fdqty            ELSE 0        END) AS TotalRevenue
FROM
    rmcloseinfo AS r
JOIN
    FdCashBak AS b ON r.InvNo = b.InvNo AND r.ShopID = b.ShopID
JOIN
    food AS f ON b.FdNo = f.FdNo COLLATE Chinese_PRC_Stroke_CI_AS AND  b.ShopID = f.ShopID
WHERE
    r.WorkDate = '20250831'
    AND r.ShopId = 2
    AND ( b.FdCName LIKE '%抖音%' OR  b.FdCName LIKE '%抖预%')
ORDER BY
    TotalRevenue DESC;