import openpyxl
import sys
import os

# The script is in 02_python_scripts, the file is in the root
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_file_path = os.path.join(project_root, '重复歌曲数量 .xlsx')
output_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report.txt')

# Ensure the output directory exists
os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

try:
    # Load the workbook
    workbook = openpyxl.load_workbook(input_file_path, data_only=True)
    
    # Open the output file with UTF-8 encoding
    with open(output_file_path, 'w', encoding='utf-8') as f_out:
        # Iterate through each sheet
        for sheet_name in workbook.sheetnames:
            f_out.write(f"--- Sheet: {sheet_name} ---\n")
            sheet = workbook[sheet_name]
            # Iterate over all rows in the sheet
            for row in sheet.iter_rows():
                # Join cell values, converting None to an empty string
                f_out.write(",".join([str(cell.value) if cell.value is not None else '' for cell in row]) + '\n')
            f_out.write("\n")
    print(f"报表成功生成: {os.path.abspath(output_file_path)}")

except FileNotFoundError:
    print(f"错误: 文件 '{input_file_path}' 未找到。")
except Exception as e:
    print(f"发生错误: {e}")