-- 1. Add new KPI columns to the summary table
ALTER TABLE Fact_Daily_Shop_Summary
ADD
    AvgPricePerBill_Buffet DECIMAL(18, 2) NULL,
    AvgPricePerGuest_Buffet DECIMAL(18, 2) NULL,
    AvgPricePerGuest_PrimeTime DECIMAL(18, 2) NULL;
GO

-- 2. Add comments to the new columns
EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'自助餐单均价 (计算方式待定)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'Fact_Daily_Shop_Summary',
    @level2type = N'COLUMN', @level2name = N'AvgPricePerBill_Buffet';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'自助餐人均价 (计算方式待定)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'Fact_Daily_Shop_Summary',
    @level2type = N'COLUMN', @level2name = N'AvgPricePerGuest_Buffet';
GO

EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'黄金档客单价 (计算方式待定)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'Fact_Daily_Shop_Summary',
    @level2type = N'COLUMN', @level2name = N'AvgPricePerGuest_PrimeTime';
GO
