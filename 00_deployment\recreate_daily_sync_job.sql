
-- ===================================================================
-- 脚本: recreate_daily_sync_job.sql
-- 功能: 重新创建每日同步RMS数据的专用作业
-- 来源: archive/adhoc_sql_scripts_pre_20250808/create_rms_sync_job.sql
-- ===================================================================

USE msdb;
GO

DECLARE @jobId BINARY(16);
DECLARE @jobName NVARCHAR(128) = N'RMS_Daily_Data_Sync';

-- 1. 如果作业已存在，则先删除，确保脚本可重复执行
SELECT @jobId = job_id FROM dbo.sysjobs WHERE name = @jobName;
IF (@jobId IS NOT NULL)
BEGIN
    EXEC dbo.sp_delete_job @job_id = @jobId, @delete_unused_schedule = 1;
    PRINT 'Existing job ' + @jobName + ' has been deleted.';
END

-- 2. 添加新作业
EXEC dbo.sp_add_job
    @job_name = @jobName,
    @enabled = 1,
    @description = N'每日早上6点，从门店服务器同步开台和预订数据到总部。使用链接服务器 cloudRms2019。';

-- 3. 添加步骤一：同步开台数据
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Step 1 - Sync Open Data',
    @subsystem = N'TSQL',
    @command = N'EXEC operatedata.dbo.usp_Sync_RMS_DailyOpenData;',
    @on_success_action = 3, -- 成功后转到下一步
    @database_name = N'operatedata';

-- 4. 添加步骤二：同步预订数据
EXEC dbo.sp_add_jobstep
    @job_name = @jobName,
    @step_name = N'Step 2 - Sync Book Data',
    @subsystem = N'TSQL',
    @command = N'EXEC operatedata.dbo.usp_Sync_RMS_DailyBookData;',
    @database_name = N'operatedata';

-- 5. 创建执行计划（每天早上6点）
EXEC dbo.sp_add_schedule
    @schedule_name = N'Daily Sync at 06:00 AM', -- 更改了计划名称以避免潜在冲突
    @freq_type = 4, -- 每天
    @freq_interval = 1,
    @active_start_time = 60000; -- 06:00:00

-- 6. 将计划附加到作业
EXEC dbo.sp_attach_schedule
    @job_name = @jobName,
    @schedule_name = N'Daily Sync at 06:00 AM';

-- 7. 将作业分配给当前服务器
EXEC dbo.sp_add_jobserver
    @job_name = @jobName,
    @server_name = N'(local)';

PRINT 'Job ' + @jobName + ' created and scheduled successfully.';
GO
