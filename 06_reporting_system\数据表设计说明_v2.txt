-------------------------------------------------------------------
第一部分：维度表 (Dimension Tables)
-------------------------------------------------------------------
维度表为数据分析提供业务上下文，定义了分析的视角。

### 1. Dim_Date (日期维度表)

- 用途: 提供统一的、丰富的日期维度信息，是所有时间序列分析（同比、环比、工作日/周末对比）的基础。
- 粒度: 每日一行。

设计思想与价值:
  职责: 预先计算并存储各类日期属性，供事实表关联。
  价值:  1. 提升性能: 避免在大型事实表上重复执行日期函数，通过索引关联提高查询效率。
         2. 增强分析能力: 支持按节假日、周末等预定义维度进行过滤与分组。
         3. 确保一致性: 集中管理日期维度，确保所有报表的口径统一。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description)                 |
| -------------------- | -------------------- | -------------------------------------- |
| DateSK               | INT (主键)           | 代理键，用于数据库内部关联。           |
| FullDate             | DATE                 | 完整的日期, 如 '2025-08-11'。          |
| Year                 | INT                  | 该日期所属的年份, 如 2025。            |
| Month                | INT                  | 该日期所属的月份, 如 8。               |
| Day                  | INT                  | 该日期是几号, 如 11。                  |
| DayOfWeek            | INT                  | 一周中的第几天 (1=周一, 7=周日)。      |
| WeekdayName_ZH       | NVARCHAR(10)         | 中文的星期名, 如 '星期一'。            |
| IsWeekend            | BIT                  | 是否为周末 (1=是, 0=否)。              |
| IsHoliday            | BIT                  | 是否为法定节假日 (1=是, 0=否)。        |
| HolidayName          | NVARCHAR(50)         | 节假日名称, 如 '国庆节'。              |


### 2. Dim_Shop (门店维度表)

- 用途: 提供所有门店的静态信息，确保门店名称、区域等信息的统一性。
- 粒度: 一个门店一行。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description)                                         |
| -------------------- | -------------------- | -------------------------------------------------------------- |
| ShopSK               | INT (主键)           | 代理键，用于数据库内部关联。                                   |
| ShopID               | INT                  | 业务主键，来自源系统 ShopInfo 表的 ShopID。                    |
| ShopName             | NVARCHAR(100)        | 门店的官方名称。                                               |
| Address              | NVARCHAR(100)        | 门店的完整地址，来自源表 address 字段。                        |
| City                 | NVARCHAR(50)         | 门店所在的城市 (可考虑从地址中提取或单独维护)。                |
| Region               | NVARCHAR(50)         | 门店所在的区域 (可考虑从地址中提取或单独维护)。                |
| OpenDate             | DATE                 | 门店的开业日期 (需将源表 OpenDate 的文本格式转换为日期格式)。    |
| IsActive             | BIT                  | 门店当前是否在用，来自源表 IsUse 字段，用于筛选在营门店。      |


### 3. Dim_TimeSlot (时段维度表)

- 用途: 统一定义业务上使用的所有特殊分析时段。
- 粒度: 一个业务时段一行。

| 字段名 (Column Name)  | 数据类型 (Data Type) | 中文含义 (Description)                                               |
| --------------------- | -------------------- | -------------------------------------------------------------------- |
| TimeSlotSK            | INT (主键)           | 代理键，用于数据库内部关联。                                         |
| TimeSlotBusinessKey   | NVARCHAR(10)         | 业务主键, 来自源系统 timeinfo 表的 TimeNo。                          |
| TimeSlotName          | NVARCHAR(50)         | 时段的名称, 如 '10:50-13:50'。                                      |
| StartTime             | TIME                 | 时段的开始时间 (需将源表 BegTime 的整数格式如1700转换为时间格式)。 |
| EndTime               | TIME                 | 时段的结束时间 (需将源表 EndTime 的整数格式如2000转换为时间格式)。 |
| TimeSlotGroup         | NVARCHAR(50)         | 时段所属的大类, 如 '白天档', '晚上档'。                              |
| IsSpecial             | BIT                  | 是否为特殊时段，来自源表 IsSpecial 字段。                            |

-------------------------------------------------------------------
第二部分：事实表 (Fact Tables)
-------------------------------------------------------------------
事实表存放量化的业务度量，并与维度表关联。

### 4. Fact_Daily_TimeSlot_Summary (时段级事实表)

- 用途: 存放与每个具体“时段”相关的运营指标。
- 粒度: 1门店 x 1日期 x 1时段。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| TimeSlotSummarySK             | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date。                      |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop。                      |
| TimeSlotSK                    | INT                  | 外键，关联到 Dim_TimeSlot。                  |
| BookedRooms                   | INT                  | 该时段的预订房间数。                       |
| BookedGuests                  | INT                  | 该时段的预订人数。                         |
| OccupiedRooms                 | INT                  | 该时段的待客房间数。                       |
| OccupiedGuests                | INT                  | 该时段的待客人数。                         |
| OccupancyRate                 | DECIMAL(5, 4)        | 该时段的开房率 (计算得出)。                |
| Revenue                       | DECIMAL(18, 2)       | 该时段产生的总营业额。                     |
| Revenue_By_Channel_KPlus      | DECIMAL(18, 2)       | 该时段内，K+渠道产生的收入。               |
| Revenue_By_Channel_Special    | DECIMAL(18, 2)       | 该时段内，特权预约渠道产生的收入。         |
| Revenue_By_Channel_Meituan    | DECIMAL(18, 2)       | 该时段内，美团渠道产生的收入。             |
| Revenue_By_Channel_Douyin     | DECIMAL(18, 2)       | 该时段内，抖音渠道产生的收入。             |
| Revenue_By_Channel_RoomFee    | DECIMAL(18, 2)       | 该时段内，纯房费产生的收入。               |
| DirectFall_Batches            | INT                  | 该时段的直落批次数。                       |
| CreateTime                    | DATETIME             | 创建时间。                                 |
| UpdateTime                    | DATETIME             | 最后更新时间。                             |
| OperatorId                    | INT                  | 操作人ID。                                 |


### 5. Fact_Daily_Shop_Summary (门店级事实表)

- 用途: 存放按“天”汇总的全局性、总结性指标。
- 粒度: 1门店 x 1日期。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| ShopSummarySK                 | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date。                      |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop。                      |
| TotalRevenue                  | DECIMAL(18, 2)       | 全天总营业额。                             |
| DayTimeRevenue                | DECIMAL(18, 2)       | 白天档总营业额 (由各时段数据汇总)。        |
| NightTimeRevenue              | DECIMAL(18, 2)       | 晚上档总营业额 (由各时段数据汇总)。        |
| TotalBatches                  | INT                  | 全天总批次数。                             |
| BuffetGuestCount              | INT                  | 全天自助餐总人数。                         |
| TotalDirectFallGuests         | INT                  | 全天直落总人数。                           |
| ComplimentaryBatches          | INT                  | 全天招待总批次数。                         |
| ComplimentaryRevenue          | DECIMAL(18, 2)       | 全天招待总金额。                           |
| PrivilegeBooking_Count_0Yuan  | INT                  | 0元特权预约的执行次数。                    |
| PrivilegeBooking_Count_5Yuan  | INT                  | 5元特权预约的执行次数。                    |
| PrivilegeBooking_Count_10Yuan | INT                  | 10元特权预约的执行次数。                   |
| PrivilegeBooking_Count_15Yuan | INT                  | 15元特权预约的执行次数。                   |
| Fee_Meituan_Booking           | DECIMAL(18, 2)       | 支付给美团的预约类总手续费。               |
| Fee_Meituan_GroupBuy          | DECIMAL(18, 2)       | 支付给美团的团购类总手续费。               |
| Fee_Douyin_Booking            | DECIMAL(18, 2)       | 支付给抖音的预约类总手续费。               |
| Fee_Douyin_GroupBuy           | DECIMAL(18, 2)       | 支付给抖音的团购类总手续费。               |
| Fee_Bank_GF                   | DECIMAL(18, 2)       | 支付给广发银行的总手续费。                 |
| Fee_Bank_CITIC                | DECIMAL(18, 2)       | 支付给中信银行的总手续费。                 |
| Fee_Bank_UnionPay             | DECIMAL(18, 2)       | 支付给银联的总手续费。                     |
| --- 夜间细分指标 ---          | ---                  | ---                                        |
| Night_FreeMeal_Batches        | INT                  | 夜间自由餐批次数。                         |
| Night_FreeMeal_Revenue        | DECIMAL(18, 2)       | 夜间自由餐营收。                           |
| Night_Buyout_Batches          | INT                  | 夜间买断批次数。                           |
| Night_DrinkPackage_Batches    | INT                  | 夜间畅饮套餐批次数。                       |
| Night_FreeConsumption_Batches | INT                  | 夜间自由消费套餐批次数。                   |
| Night_RoomFee_Batches         | INT                  | 夜间房费批次数。                           |
| Night_Other_Batches           | INT                  | 夜间其他批次数。                           |
| CreateTime                    | DATETIME             | 创建时间。                                 |
| UpdateTime                    | DATETIME             | 最后更新时间。                             |
| OperatorId                    | INT                  | 操作人ID。                                 |
| --- 储值相关指标 ---          | ---                  | ---                                        |
| DailyRecharge_Total           | DECIMAL(18, 2)       | 当天充值总额 (线上+线下)。                 |
| DailyRecharge_Meituan         | DECIMAL(18, 2)       | 当天新美大渠道充值总额。                   |
| DailyRecharge_Meituan_Ratio   | DECIMAL(5, 4)        | 当天新美大充值占比。                       |
| RechargeTier1_BatchCount      | INT                  | 充值档位(1-499元)批次数。                  |
| RechargeTier1_TotalAmount     | DECIMAL(18, 2)       | 充值档位(1-499元)总金额。                  |
| RechargeTier2_BatchCount      | INT                  | 充值档位(500-999元)批次数。                |
| RechargeTier2_TotalAmount     | DECIMAL(18, 2)       | 充值档位(500-999元)总金额。                |
| RechargeTier3_BatchCount      | INT                  | 充值档位(1000-1999元)批次数。              |
| RechargeTier3_TotalAmount     | DECIMAL(18, 2)       | 充值档位(1000-1999元)总金额。              |
| RechargeTier4_BatchCount      | INT                  | 充值档位(2000-2999元)批次数。              |
| RechargeTier4_TotalAmount     | DECIMAL(18, 2)       | 充值档位(2000-2999元)总金额。              |
| RechargeTier5_BatchCount      | INT                  | 充值档位(3000-4999元)批次数。              |
| RechargeTier5_TotalAmount     | DECIMAL(18, 2)       | 充值档位(3000-4999元)总金额。              |
| RechargeTier6_BatchCount      | INT                  | 充值档位(5000-9999元)批次数。              |
| RechargeTier6_TotalAmount     | DECIMAL(18, 2)       | 充值档位(5000-9999元)总金额。              |
| RechargeTier7_BatchCount      | INT                  | 充值档位(10000-19999元)批次数。            |
| RechargeTier7_TotalAmount     | DECIMAL(18, 2)       | 充值档位(10000-19999元)总金额。            |
| RechargeTier8_BatchCount      | INT                  | 充值档位(20000元及以上)批次数。            |
| RechargeTier8_TotalAmount     | DECIMAL(18, 2)       | 充值档位(20000元及以上)总金额。            |


### 6. Fact_Daily_BatchSize_Summary (日批次人数及金额汇总事实表)

- **用途:** 存放按不同批次人数（2人, 3人...）统计的业务指标（批次数及总金额），以支持更精细化的客群及消费能力分析。
- **粒度:** 1门店 x 1日期 x 1业务场景（如自助餐、黄金档）。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| BatchSizeSummarySK            | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date。                      |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop。                      |
| Category                      | NVARCHAR(50)         | 业务场景, 如 '自助餐', '黄金档'。         |
| TotalBatches                  | INT                  | 该场景下的总批次数。                       |
| TotalAmount                   | DECIMAL(18, 2)       | 该场景下的总金额。                         |
| Batches_2_Person              | INT                  | 2人批次数。                                |
| Amount_2_Person               | DECIMAL(18, 2)       | 2人批次的总金额。                          |
| Batches_3_Person              | INT                  | 3人批次数。                                |
| Amount_3_Person               | DECIMAL(18, 2)       | 3人批次的总金额。                          |
| Batches_4_Person              | INT                  | 4人批次数。                                |
| Amount_4_Person               | DECIMAL(18, 2)       | 4人批次的总金额。                          |
| Batches_5_Person              | INT                  | 5人批次数。                                |
| Amount_5_Person               | DECIMAL(18, 2)       | 5人批次的总金额。                          |
| Batches_6_Person              | INT                  | 6人批次数。                                |
| Amount_6_Person               | DECIMAL(18, 2)       | 6人批次的总金额。                          |
| Batches_7_Person              | INT                  | 7人批次数。                                |
| Amount_7_Person               | DECIMAL(18, 2)       | 7人批次的总金额。                          |
| Batches_8_Person              | INT                  | 8人批次数。                                |
| Amount_8_Person               | DECIMAL(18, 2)       | 8人批次的总金额。                          |
| Batches_9_Person              | INT                  | 9人批次数。                                |
| Amount_9_Person               | DECIMAL(18, 2)       | 9人批次的总金额。                          |
| Batches_10_Plus_Person        | INT                  | 10人及以上批次数。                         |
| Amount_10_Plus_Person         | DECIMAL(18, 2)       | 10人及以上批次的总金额。                   |
| CreateTime                    | DATETIME             | 创建时间。                                 |
| UpdateTime                    | DATETIME             | 最后更新时间。                             |
| OperatorId                    | INT                  | 操作人ID。                                 |


### 7. Fact_Monthly_Shop_Summary (月度门店汇总事实表)

- **用途:** 存放按“月”聚合的最终财务和运营指标，用于管理层进行趋势分析和战略决策。
- **粒度:** 1门店 x 1月份。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
|---|---|---|
| Year | INT | 年份，主键之一 |
| Month | INT | 月份，主键之一 |
| ShopID | INT | 门店ID，主键之一 |
| Revenue_Total | DECIMAL(18, 2) | 月度总营业额 |
| Revenue_Total_YoY | FLOAT | 总营业额年同比增减率 |
| Revenue_Total_MoM | FLOAT | 总营业额月环比增减率 |
| Revenue_Offline | DECIMAL(18, 2) | 月度线下门店营收 |
| Revenue_Offline_YoY | FLOAT | 线下门店营收年同比增减率 |
| Revenue_Offline_MoM | FLOAT | 线下门店营收月环比增减率 |
| Revenue_PrivilegeBooking | DECIMAL(18, 2) | 月度特权预约总收入 |
| Revenue_PrivilegeBooking_YoY | FLOAT | 特权预约收入年同比增减率 |
| Revenue_PrivilegeBooking_MoM | FLOAT | 特权预约收入月环比增减率 |
| Revenue_OfficialAccount | DECIMAL(18, 2) | 月度公众号总收入 |
| Revenue_OfficialAccount_YoY | FLOAT | 公众号收入年同比增减率 |
| Revenue_OfficialAccount_MoM | FLOAT | 公众号收入月环比增减率 |
| Revenue_Meituan | DECIMAL(18, 2) | 月度美团总收入 |
| Revenue_Meituan_YoY | FLOAT | 美团收入年同比增减率 |
| Revenue_Meituan_MoM | FLOAT | 美团收入月环比增减率 |
| Revenue_Douyin | DECIMAL(18, 2) | 月度抖音总收入 |
| Revenue_Douyin_YoY | FLOAT | 抖音收入年同比增减率 |
| Revenue_Douyin_MoM | FLOAT | 抖音收入月环比增减率 |
| Revenue_Bank | DECIMAL(18, 2) | 月度银行渠道总收入 |
| Revenue_Bank_YoY | FLOAT | 银行渠道收入年同比增减率 |
| Revenue_Bank_MoM | FLOAT | 银行渠道收入月环比增减率 |
| Revenue_OtherBusiness | DECIMAL(18, 2) | 月度其他异业合作总收入 |
| Revenue_OtherBusiness_YoY | FLOAT | 其他异业收入年同比增减率 |
| Revenue_OtherBusiness_MoM | FLOAT | 其他异业收入月环比增减率 |
| Count_PrivilegeBooking_0Yuan | INT | 月度0元特权预约执行次数 |
| Count_PrivilegeBooking_5Yuan | INT | 月度5元特权预约执行次数 |
| Count_PrivilegeBooking_10Yuan | INT | 月度10元特权预约执行次数 |
| Count_PrivilegeBooking_15Yuan | INT | 月度15元特权预约执行次数 |
| Revenue_Douyin_Booking | DECIMAL(18, 2) | 月度抖音预约收入 |
| Revenue_Douyin_GroupBuy | DECIMAL(18, 2) | 月度抖音团购收入 |
| Revenue_Meituan_Booking | DECIMAL(18, 2) | 月度美团预约收入 |
| Revenue_Meituan_GroupBuy | DECIMAL(18, 2) | 月度美团团购收入 |
| Revenue_Bank_GF | DECIMAL(18, 2) | 月度广发银行收入 |
| Revenue_Bank_CITIC | DECIMAL(18, 2) | 月度中信银行收入 |
| Revenue_Bank_UnionPay | DECIMAL(18, 2) | 月度银联收入 |
| Fee_Douyin_Booking | DECIMAL(18, 2) | 月度抖音预约手续费 |
| Fee_Douyin_GroupBuy | DECIMAL(18, 2) | 月度抖音团购手续费 |
| Fee_Meituan_Booking | DECIMAL(18, 2) | 月度美团预订手续费 |
| Fee_Meituan_GroupBuy | DECIMAL(18, 2) | 月度美团团购手续费 |
| Fee_Bank_Total | DECIMAL(18, 2) | 月度银行渠道总手续费 |


### 8. Fact_StoredValue_Transaction (储值流水事实表)

- **用途:** 记录每一笔会员储值操作的详细流水，是分析储值行为、渠道贡献和会员价值的基础。
- **粒度:** 每一笔储值交易为一行。

| 字段名 (Column Name)          | 数据类型 (Data Type) | 中文含义 (Description)                     |
| ----------------------------- | -------------------- | ------------------------------------------ |
| StoredValueTransactionSK      | BIGINT (主键)        | 代理键。                                   |
| DateSK                        | INT                  | 外键，关联到 Dim_Date，代表充值发生的日期。 |
| ShopSK                        | INT                  | 外键，关联到 Dim_Shop，代表充值发生的门店。 |
| MemberName                    | NVARCHAR(100)        | 会员姓名。                                 |
| MemberPhone                   | NVARCHAR(50)         | 会员手机号码，用于关联会员信息。           |
| RechargeAmount                | DECIMAL(18, 2)       | 本次储值的金额。                           |
| FrozenAmount                  | DECIMAL(18, 2)       | 本次储值后被冻结的金额。                   |
| RechargeChannel               | NVARCHAR(50)         | 充值场景或渠道，如 '线上', '线下'。        |
| CommissionAmount              | DECIMAL(18, 2)       | 该笔储值产生的提成金额。                   |
| CommissionRecipient           | NVARCHAR(50)         | 获得该笔提成的员工或角色。               |
| OfferName                     | NVARCHAR(100)        | 储值时享受的优惠活动名称。               |
| BillStatus                    | NVARCHAR(50)         | 账单状态，如 '已结账', '挂账'。           |
| InvNo                         | NVARCHAR(50)         | 源系统订单号 (InvNo)，用于关联追溯。      |
| FdNo                          | NVARCHAR(50)         | 源系统商品号 (FdNo)，用于关联追溯。      |
| OperatorId                    | INT                  | 操作人ID。                                 |
| SourceSheet                   | NVARCHAR(50)         | 源数据在Excel中的工作表名，用于溯源。    |
| CreateTime                    | DATETIME             | 创建时间。                                 |
| UpdateTime                    | DATETIME             | 最后更新时间。                             |


### 9. Dim_Coupon (卡券维度表)

- **用途:** 存放每一种卡券的静态信息，如名称、类型、面值等，是卡券分析的核心维度。
- **粒度:** 每一种卡券为一行。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| CouponSK | INT (主键) | 代理键 |
| CouponBusinessKey | NVARCHAR(50) | 业务主键，来自源系统的卡券ID |
| CouponName | NVARCHAR(100) | 卡券名称 |
| CouponType | NVARCHAR(50) | 卡券类型, 如 '代金券', '折扣券' |
| FaceValue | DECIMAL(18, 2) | 面值 |
| IssuingCampaign | NVARCHAR(100) | 所属的发行项目或活动 |
| ValidStartDate | DATE |有效期开始日期 |
| ValidEndDate | DATE | 有效期结束日期 |


### 10. Fact_Daily_Coupon_Summary (日卡券汇总事实表)

- **用途:** 记录每种卡券在每个门店每天的发行、使用、过期等核心指标。
- **粒度:** 1天 x 1门店 x 1卡券。

| 字段名 (Column Name) | 数据类型 (Data Type) | 中文含义 (Description) |
| :--- | :--- | :--- |
| DailyCouponSummarySK | BIGINT (主键) | 代理键 |
| DateSK | INT | 外键，关联到 Dim_Date |
| ShopSK | INT | 外键，关联到 Dim_Shop |
| CouponSK | INT | 外键，关联到 Dim_Coupon |
| IssuedCount | INT | 发放数量 |
| IssuedRevenue | DECIMAL(18, 2) | 发放标准实收 |
| UsedCount | INT | 使用数量 |
| DiscountAmount | DECIMAL(18, 2) | 优惠金额 |
| UsedRevenue | DECIMAL(18, 2) | 使用标准实收 |
| DrivenRevenue | DECIMAL(18, 2) | 带动营业额 |
| RevokedCount | INT | 撤回数量 |
| ExpiredCount | INT | 过期数量 |
| VoidedCount | INT | 作废数量 |