import pyodbc

sql_create_sp = """
CREATE PROCEDURE usp_Populate_Fact_Daily_BatchSize_Summary
    @RunDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    -- 仅包含删除语句，用于测试
    DELETE FROM dbo.Fact_Daily_BatchSize_Summary
    WHERE WorkDate = @RunDate;

    PRINT 'Minimal shell procedure created successfully.';

    SET NOCOUNT OFF;
END;
"""

# Database connection details
conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=***********;"
    "DATABASE=operatedata;"
    "UID=sa;"
    "PWD=Musicbox123;"
    "TrustServerCertificate=yes;"
)

# Connect to the database and execute the script
try:
    print("Connecting to the database...")
    conn = pyodbc.connect(conn_str)
    cursor = conn.cursor()
    print("Connection successful. Executing script to create minimal stored procedure...")
    
    # Execute the T-SQL script
    cursor.execute(sql_create_sp)
    conn.commit()
    
    print("Successfully created the minimal stored procedure.")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"An error occurred: {sqlstate}")
    print(ex)

finally:
    if 'conn' in locals() and conn:
        conn.close()
        print("Database connection closed.")
