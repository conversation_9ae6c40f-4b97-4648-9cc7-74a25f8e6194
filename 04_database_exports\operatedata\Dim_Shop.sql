/*
 Navicat Premium Dump SQL

 Source Server         : 数据库5
 Source Server Type    : SQL Server
 Source Server Version : 11003000 (11.00.3000)
 Source Host           : ***********:1433
 Source Catalog        : OperateData
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11003000 (11.00.3000)
 File Encoding         : 65001

 Date: 26/08/2025 15:54:51
*/


-- ----------------------------
-- Table structure for Dim_Shop
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dim_Shop]') AND type IN ('U'))
	DROP TABLE [dbo].[Dim_Shop]
GO

CREATE TABLE [dbo].[Dim_Shop] (
  [ShopSK] int  IDENTITY(1,1) NOT NULL,
  [ShopID] int  NOT NULL,
  [ShopName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [Address] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [City] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Region] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [OpenDate] date  NULL,
  [IsActive] bit  NOT NULL
)
GO

ALTER TABLE [dbo].[Dim_Shop] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for Dim_Shop
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[Dim_Shop]', RESEED, 19)
GO


-- ----------------------------
-- Indexes structure for table Dim_Shop
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [UIX_Dim_Shop_ShopID]
ON [dbo].[Dim_Shop] (
  [ShopID] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table Dim_Shop
-- ----------------------------
ALTER TABLE [dbo].[Dim_Shop] ADD CONSTRAINT [PK__Dim_Shop__67C5017B5CBCE000] PRIMARY KEY CLUSTERED ([ShopSK])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

