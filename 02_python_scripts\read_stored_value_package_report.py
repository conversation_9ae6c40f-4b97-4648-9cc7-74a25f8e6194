import pandas as pd
import os

file_name = '储值套餐统计表.xlsx'
file_path = os.path.join(os.getcwd(), file_name)

def read_and_combine_sheets():
    """
    Reads all sheets from the specified Excel file, combines them,
    and prints the resulting table and its structure.
    """
    try:
        if not os.path.exists(file_path):
            print(f"错误：找不到文件 '{file_path}'")
            return

        print(f"正在读取文件: {file_path}")
        all_sheets_dict = pd.read_excel(file_path, engine='openpyxl', sheet_name=None)

        if not all_sheets_dict:
            print("错误：Excel文件中没有找到任何工作表。")
            return

        print(f"找到 {len(all_sheets_dict)} 个工作表: {list(all_sheets_dict.keys())}")

        # 为每个DataFrame添加一列来标识原始工作表
        for sheet_name, df in all_sheets_dict.items():
            df['SourceSheet'] = sheet_name

        combined_df = pd.concat(all_sheets_dict.values(), ignore_index=True)

        print("\n--- 合并后的数据 ---")
        print(combined_df.to_string())
        print("\n--------------------")

        print("\n--- 数据表结构 (用于文档) ---")
        print(f"建议表名: Dim_StoredValue_Package")
        for col in combined_df.columns:
            col_type = str(combined_df[col].dtype)
            if col_type == 'object':
                col_type = 'NVARCHAR(255)' # Default for object types
            elif col_type == 'int64':
                col_type = 'INT'
            elif col_type == 'float64':
                col_type = 'DECIMAL(18, 2)'
            elif 'datetime' in col_type:
                col_type = 'DATETIME'
            print(f"- {col} ({col_type})")

    except Exception as e:
        print(f"读取或处理文件时发生错误: {e}")

if __name__ == '__main__':
    read_and_combine_sheets()