import pyodbc

# Database connection details
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\alter_sp_job_orchestrator.sql'

cnxn = None
cursor = None
try:
    with open(sql_file_path, 'r', encoding='utf-8-sig') as f:
        sql_script = f.read()

    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    print("Altering the main ETL orchestrator stored procedure...")
    cursor.execute(sql_script)
    cnxn.commit()
    print("Successfully updated usp_Job_Run_Daily_ETL.")
    print("The automation is now fully configured.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
