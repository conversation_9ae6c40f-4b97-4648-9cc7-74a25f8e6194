
# KTV 数据仓库 ETL 流程说明文档

**版本**: 1.0
**最后更新**: 2025-09-01

## 1. 高级概述

本ETL（数据抽取、转换、加载）流程旨在每日自动从源业务数据库中提取数据，经过计算和聚合，最终填充到数据仓库的各个核心事实表中，为管理层报表提供准确、一致的数据支持。

## 2. 数据流转架构

核心思想是“暂存表 + 合并”模式，确保数据处理的原子性和可维护性。

`源数据表 (rmcloseinfo, fdcashbak, ...)` -> `计算存储过程 (usp_Calculate_*)` -> `物理暂存表 (stg_*)` -> `主填充过程 (usp_Populate_*)` -> `最终事实表 (Fact_*)`

## 3. 自动化执行入口

- **对象类型**: SQL Server 代理作业
- **作业名称**: `KTV_Daily_ETL_Processing`
- **执行计划**: 每日早上 8:30
- **执行内容**: 调用唯一的总调度存储过程 `usp_Job_Run_Daily_ETL`。

---

## 4. 关键对象清单 (核心存储过程)

### 4.1. 总调度层

#### `usp_Job_Run_Daily_ETL`
- **用途**: 整个每日ETL流程的最高指挥官，是定时作业唯一调用的入口。
- **参数**: 无。
- **核心逻辑**:
    1. 设定目标日期为“昨天”。
    2. 调用 `usp_Populate_Fact_Daily_BatchSize_Summary`，完成批次规模汇总表的计算。
    3. 获取所有有效门店列表，并使用游标（Cursor）循环。
    4. 在循环中，为每个门店调用 `usp_Populate_Fact_Daily_Shop_Summary`，完成每日门店汇总表的计算。

### 4.2. 主填充层

#### `usp_Populate_Fact_Daily_Shop_Summary`
- **用途**: 负责填充 `Fact_Daily_Shop_Summary`（每日门店汇总表）的核心主过程。
- **参数**: `@TargetDate`, `@ShopId`
- **调用关系**:
    - 被 `usp_Job_Run_Daily_ETL` 调用。
    - 调用 `usp_Calculate_DayTimeMetrics_ForStaging` 和 `usp_Calculate_Recharge_ForStaging`。
- **核心逻辑**:
    1. 根据输入参数，准备好 `stg_Fact_Daily_Shop_Summary` 暂存表（删除旧数据，插入带Key的新行）。
    2. 调用所有“计算工人”（`usp_Calculate_*`系列），让它们将结果更新到暂存表的对应行中。
    3. 所有计算完成后，使用 `MERGE` 语句，将暂存表中完整的数据原子性地插入或更新到最终的 `Fact_Daily_Shop_Summary` 表中。

#### `usp_Populate_Fact_Daily_BatchSize_Summary` (修正版)
- **用途**: 负责填充 `Fact_Daily_BatchSize_Summary`（批次规模汇总表）。
- **参数**: `@RunDate`
- **调用关系**:
    - 被 `usp_Job_Run_Daily_ETL` 调用。
- **核心逻辑**:
    1. 删除目标日期的旧数据。
    2. 使用 `WITH ... AS (SELECT DISTINCT InvNo, ...)` 结构，从源数据表 `rmcloseinfo` 和 `fdcashbak` 中提取账单数据，从根源上解决了重复计算导致金额虚高的问题。
    3. 将计算和聚合后的结果直接 `INSERT` 到 `Fact_Daily_BatchSize_Summary` 表中。

### 4.3. 计算层 (工人)

#### `usp_Calculate_DayTimeMetrics_ForStaging`
- **用途**: 专用的“计算工人”，负责计算日间/夜间相关的核心指标（如营收、批次、人数、客单价等）。
- **参数**: `@TargetDate`, `@ShopId`, `@DateSK`, `@ShopSK`
- **调用关系**:
    - 被 `usp_Populate_Fact_Daily_Shop_Summary` 调用。
- **核心逻辑**: 从 `rmcloseinfo` 表中计算指标，并 `UPDATE` 到 `stg_Fact_Daily_Shop_Summary` 暂存表中。

#### `usp_Calculate_Recharge_ForStaging`
- **用途**: 专用的“计算工人”，负责计算所有充值相关的分档指标。
- **参数**: `@TargetDate`, `@ShopId`, `@DateSK`, `@ShopSK`
- **调用关系**:
    - 被 `usp_Populate_Fact_Daily_Shop_Summary` 调用。
- **核心逻辑**: 从 `mims.dbo.RechargedataInfo` 表中计算指标，并 `UPDATE` 到 `stg_Fact_Daily_Shop_Summary` 暂存表中。

### 4.4. 通用工具层

#### `usp_Util_CalculateDayTimeMetrics`
- **用途**: 一个独立的、向后兼容的工具函数，用于计算日间/夜间指标。
- **参数**: `@TargetDate`, `@ShopId`
- **调用关系**:
    - 被旧的报表流程 `usp_Report_CreateDailyPerformance` 调用。
    - **不被**我们新的ETL主流程调用。
- **核心逻辑**: 执行计算，并直接 `SELECT` 返回一个结果集，不修改任何物理表。
