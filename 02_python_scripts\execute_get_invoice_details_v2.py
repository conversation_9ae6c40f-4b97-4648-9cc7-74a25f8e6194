import pyodbc

# Database connection details
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
invoice_no = '*********'

sql_query_1 = f"SELECT InvNo, Numbers, CloseDatetime FROM operatedata.dbo.rmcloseinfo WHERE InvNo = '{invoice_no}'"
sql_query_2 = f"SELECT FdCName, FdQty FROM operatedata.dbo.fdcashbak WHERE InvNo = '{invoice_no}' AND FdCName LIKE N'%消费人数%' AND CashType = 'N'"

cnxn = None
cursor = None
try:
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()

    # --- Execute and print first query ---
    print("--- Final Bill Information (from rmcloseinfo) ---")
    cursor.execute(sql_query_1)
    row = cursor.fetchone()
    if row:
        print(f"Invoice: {row.InvNo}")
        print(f"Final Guest Count (Numbers): {row.Numbers}")
        print(f"Closing Time: {row.CloseDatetime}")
    else:
        print("No final bill record found.")

    print("\n--- Guest-related Items (from fdcashbak) ---")
    # --- Execute and print second query ---
    cursor.execute(sql_query_2)
    rows = cursor.fetchall()
    if rows:
        total_qty = 0
        print(f"{'Item Name':<25} | {'Quantity (FdQty)'}")
        print('-' * 40)
        for row in rows:
            print(f"{row.FdCName:<25} | {row.FdQty}")
            total_qty += row.FdQty
        print('-' * 40)
        print(f"{'Total Quantity from Items:':<25} | {total_qty}")
    else:
        print("No '消费人数' items found for this bill.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
