- Always respond in 中文 with utf-8 encoding.
- 如果一个脚本或文件执行或修改失败，并且尝试修复后再次失败，我应该放弃继续修改，并创建一个全新的文件来执行任务，以避免潜在的文件缓存或状态问题。
- KTV房间状态码中文含义：A=结帐, B=坏房, C=续单, D=清洁, E=空房, H=预转, L=慢带, U=占用, R=留房, W=派房, V=微信, F=预结。
- 从总部服务器(***********)访问门店服务器(*************)的rms2019数据库时，应使用名为 'cloudRms2019' 的链接服务器。例如：cloudRms2019.rms2019.dbo.opencacheinfo。
- KTV项目的数据同步新方案：1. 目标是同步opencacheinfo, openhistory, bookcacheinfo, bookhistory四个表。2. 数据源是链接服务器cloudRms2019。3. 开台数据需增加并计算'WorkDate'(营业日，早上9点为界)，预订数据按自然日同步。4. 通过两个新的独立存储过程(usp_Sync_RMS_DailyOpenData, usp_Sync_RMS_DailyBookData)和新的定时作业(RMS_Daily_Data_Sync)实现。
- KTV_Data_Analysis项目文件结构规范：新文件应根据类型放入对应的数字编号文件夹：00_deployment (部署脚本), 01_sql_scripts (SQL脚本, 内含reports, maintenance, tests_and_adhoc子文件夹), 02_python_scripts (Python脚本), 03_reports (报告, 内含analysis_results, final_reports_and_docs子文件夹), 04_database_exports (数据库导出), 05_documentation (文档)。旧文件应移至archive/文件夹。
- KTV_Data_Analysis项目信息获取优先级：1. 首先，分析项目中的 .md 和 .sql 文件以了解背景和历史。2. 其次，如果文件信息不足，则连接数据库 (***********) 获取实时数据和对象结构。
- KTV数据库架构：总部服务器(***********)拥有库：operatedata(运营库)、mims(会员库)、rms2019(同步目标库)和一个本地dbfood库。每个远程门店(如*************)拥有自己的rms2019(同步源)和dbfood库。总部通过链接服务器(如cloudRms2019)连接门店rms2019。核心任务是将门店rms2019数据同步到总部rms2019。各dbfood库相互独立，不进行同步。
- 核心诊断：总部服务器(***********)的每日数据同步作业'RMS_Daily_Data_Sync_to_HQ'若未执行，极有可能是被意外删除。应首先检查作业是否存在，如不存在，则使用'00_deployment/04_create_sql_agent_job.sql'脚本进行重建。
- 所有新的 .py 脚本文件应创建在 `02_python_scripts/` 目录下。
- KTV报表核心业务逻辑：有效预订需过滤isdelete字段；待客人数的唯一来源是opencacheinfo/openhistory的Numbers字段，因为FdCashBak中不存在"消费人数"项；时间段统计应使用Beg_Key和End_Key。
- 在KTV项目中，包含消费项详情的核心表是 FdCashBak，它位于本地的 operatedata 数据库，而不是远程的 Dbfood 数据库。
- 技术准则：使用sqlcmd执行复杂查询时，应优先将SQL语句写入.sql文件，然后使用 -i 参数执行，以避免-Q参数的引号和转义问题。

# 开发指南

## 哲学理念

### 核心信念

- **增量进展优于大爆炸式变更** - 小规模变更，确保能编译并通过测试
- **从现有代码中学习** - 在实现前先研究和规划
- **务实胜过教条** - 适应项目实际情况
- **明确意图胜过聪明代码** - 保持简单明了

### 简单性意味着

- 每个函数/类只承担单一职责
- 避免过早抽象
- 不使用 clever tricks - 选择简单的解决方案
- 如果需要解释，说明太复杂了

## 过程

### 1. 规划与分阶段

将复杂工作分解为3-5个阶段。在`IMPLEMENTATION_PLAN.md`中记录：

```markdown
## 阶段 N: [名称]
**目标**: [具体交付物]
**成功标准**: [可测试的结果]
**测试**: [具体测试用例]
**状态**: [未开始|进行中|已完成]
```
- 随着进展更新状态
- 所有阶段完成后移除文件

### 2. 实现流程

1. **理解** - 研究代码库中的现有模式
2. **测试** - 先写测试（红色）
3. **实现** - 编写最少代码以通过测试（绿色）
4. **重构** - 在测试通过的情况下清理代码
5. **提交** - 使用清晰的提交信息关联计划

### 3. 遇到困难时（尝试3次后）

**重要**: 每个问题最多尝试3次，然后停止。

1. **记录失败原因**:
   - 你尝试了什么
   - 具体的错误信息
   - 你认为失败的原因

2. **研究替代方案**:
   - 寻找2-3个类似实现
   - 注意使用了哪些不同的方法

3. **质疑基础**:
   - 这是正确的抽象层次吗？
   - 能否拆分成更小的问题？
   - 是否有完全不同的简单方法？

4. **尝试不同角度**:
   - 不同的库/框架功能？
   - 不同的架构模式？
   - 移除抽象而不是添加？

## 技术标准

### 架构原则

- **组合优于继承** - 使用依赖注入
- **接口优于单例** - 便于测试和灵活性
- **明确优于隐式** - 清晰的数据流和依赖关系
- **尽可能测试驱动** - 永不禁用测试，而是修复它们

### 代码质量

- **每次提交必须**:
  - 成功编译
  - 通过所有现有测试
  - 包含新功能的测试
  - 遵循项目格式化/代码检查规范

- **提交前**:
  - 运行格式化工具/代码检查
  - 自我审查变更
  - 确保提交信息解释"为什么"

### 错误处理

- 快速失败并提供描述性信息
- 包含调试上下文
- 在适当级别处理错误
- 永不静默吞掉异常

## 决策框架

当存在多个有效方法时，基于以下标准选择：

1. **可测试性** - 我能轻松测试吗？
2. **可读性** - 6个月后有人能理解吗？
3. **一致性** - 这是否符合项目模式？
4. **简单性** - 这是最简单有效的解决方案吗？
5. **可逆性** - 以后更改有多难？

## 项目集成

### 学习代码库

- 寻找3个类似的功能/组件
- 识别通用模式和约定
- 尽可能使用相同的库/工具
- 遵循现有的测试模式

### 工具

- 使用项目现有的构建系统
- 使用项目的测试框架
- 使用项目的格式化工具/代码检查设置
- 没有充分理由不要引入新工具

## 质量门禁

### 完成的定义

- [ ] 编写并通过了测试
- [ ] 代码遵循项目约定
- [ ] 没有代码格式化/检查警告
- [ ] 提交信息清晰
- [ ] 实现与计划匹配
- [ ] 没有无问题编号的TODO

### 测试指南

- 测试行为，而不是实现
- 尽可能每个测试一个断言
- 清晰的测试名称描述场景
- 使用现有的测试工具/助手
- 测试应该是确定性的

## 重要提醒

**永不**:
- 使用 `--no-verify` 绕过提交钩子
- 禁用测试而不是修复它们
- 提交无法编译的代码
- 做出假设 - 用现有代码验证

**始终**:
- 增量提交可工作的代码
- 随着进展更新计划文档
- 从现有实现中学习
- 尝试3次失败后停止并重新评估
- 保存一些你尝试过的，觉得重要的核心使用方式，不论是代码相关还是sql相关或者命令行，到memory里面