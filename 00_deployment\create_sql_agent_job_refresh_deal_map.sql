USE msdb;
GO

-- ==============================================
-- Description: Creates a daily job to refresh the Dim_Deal_Map table.
-- ==============================================

DECLARE @jobId BINARY(16)

-- Check if the job already exists and delete it to ensure a clean setup
IF EXISTS (SELECT job_id FROM msdb.dbo.sysjobs_view WHERE name = N'Refresh_Dim_Deal_Map_Daily')
BEGIN
    PRINT 'Job "Refresh_Dim_Deal_Map_Daily" already exists. Deleting it before recreating.';
    EXEC msdb.dbo.sp_delete_job @job_name=N'Refresh_Dim_Deal_Map_Daily', @delete_unused_schedule=1;
END

PRINT 'Creating job "Refresh_Dim_Deal_Map_Daily"...';

-- 1. Add the Job
EXEC  msdb.dbo.sp_add_job
    @job_name=N'Refresh_Dim_Deal_Map_Daily',
    @enabled=1,
    @notify_level_eventlog=0,
    @notify_level_email=0,
    @notify_level_netsend=0,
    @notify_level_page=0,
    @delete_level=0,
    @description=N'Daily job to refresh the Dim_Deal_Map table by executing usp_Refresh_Dim_Deal_Map.',
    @category_name=N'[Uncategorized (Local)]',
    @owner_login_name=N'sa',
    @job_id = @jobId OUTPUT;

-- 2. Add the Job Step to execute the stored procedure
PRINT 'Adding job step to execute usp_Refresh_Dim_Deal_Map...';
EXEC msdb.dbo.sp_add_jobstep
    @job_id=@jobId,
    @step_name=N'Execute_Refresh_Procedure',
    @step_id=1,
    @cmdexec_success_code=0,
    @on_success_action=1, -- Quit with success
    @on_fail_action=2,    -- Quit with failure
    @retry_attempts=0,
    @retry_interval=0,
    @os_run_priority=0, @subsystem=N'TSQL',
    @command=N'EXEC operatedata.dbo.usp_Refresh_Dim_Deal_Map;',
    @database_name=N'operatedata',
    @flags=0;

-- 3. Create the Schedule for the job
PRINT 'Creating daily 3 AM schedule...';
EXEC msdb.dbo.sp_add_schedule
    @schedule_name=N'Daily_3AM_For_Deal_Map',
    @enabled=1,
    @freq_type=4, -- Daily
    @freq_interval=1,
    @freq_subday_type=1, -- At the specified time
    @freq_subday_interval=0,
    @freq_relative_interval=0,
    @freq_recurrence_factor=0,
    @active_start_date=20230101, -- A date in the past to start immediately
    @active_end_date=99991231,
    @active_start_time=30000, -- 03:00:00
    @active_end_time=235959;

-- 4. Attach the Schedule to the Job
PRINT 'Attaching schedule to the job...';
EXEC msdb.dbo.sp_attach_schedule
   @job_id = @jobId,
   @schedule_name = N'Daily_3AM_For_Deal_Map';

-- 5. Set the Job Server for the job
PRINT 'Setting job server...';
EXEC msdb.dbo.sp_add_jobserver
    @job_id = @jobId,
    @server_name = N'(local)';

PRINT 'Job "Refresh_Dim_Deal_Map_Daily" created successfully.';
GO
