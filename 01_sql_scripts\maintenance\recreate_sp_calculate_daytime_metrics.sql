/*
Recreates the usp_Calculate_DayTimeMetrics_ForStaging procedure with a corrected approach.
This version preserves the original revenue calculation from RmCloseInfo and adds
detailed channel calculations from FdCashBak, including bank ServiceFee.
Corrected FdCashBak date filtering by joining to RmCloseInfo.WorkDate.
*/

IF OBJECT_ID('usp_Calculate_DayTimeMetrics_ForStaging', 'P') IS NOT NULL
    DROP PROCEDURE usp_Calculate_DayTimeMetrics_ForStaging;
GO

CREATE PROCEDURE dbo.usp_Calculate_DayTimeMetrics_ForStaging
    @ShopId INT,
    @TargetDate DATE,
    @ShopSK INT,
    @DateSK INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @BeginDate DATETIME = DATEADD(hour, 8, CAST(@TargetDate AS DATETIME));
    DECLARE @EndDate DATETIME = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS DATETIME));

    -- CTE for Channel-specific metrics from FdCashBak
    WITH ChannelMetrics AS (
        SELECT
            b.InvNo,
            b.Shopid,
            -- Revenues
            SUM(CASE WHEN dc.ChannelName = N'美团' AND dc.SubChannelName = N'美预' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Meituan_Booking,
            SUM(CASE WHEN dc.ChannelName = N'美团' AND dc.SubChannelName = N'美团' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Meituan_GroupBuy,
            SUM(CASE WHEN dc.ChannelName = N'抖音' AND dc.SubChannelName = N'抖预' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Douyin_Booking,
            SUM(CASE WHEN dc.ChannelName = N'抖音' AND dc.SubChannelName = N'抖音' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Douyin_GroupBuy,
            SUM(CASE WHEN dc.SubChannelName = N'广发银行' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Bank_GF,
            SUM(CASE WHEN dc.SubChannelName = N'中信银行' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Bank_CITIC,
            SUM(CASE WHEN dc.SubChannelName = N'广日银联' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Bank_UnionPay,
            -- Fees (Meituan/Douyin use FeeRate from Dim_Channel)
            SUM(CASE WHEN dc.ChannelName = N'美团' AND dc.SubChannelName = N'美预' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Meituan_Booking,
            SUM(CASE WHEN dc.ChannelName = N'美团' AND dc.SubChannelName = N'美团' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Meituan_GroupBuy,
            SUM(CASE WHEN dc.ChannelName = N'抖音' AND dc.SubChannelName = N'抖预' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Douyin_Booking,
            SUM(CASE WHEN dc.ChannelName = N'抖音' AND dc.SubChannelName = N'抖音' THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Douyin_GroupBuy,
            -- Fees (Bank uses ServiceFee from Dim_Bank_Deal)
            SUM(CASE WHEN dc.ChannelName = N'银行' AND dc.SubChannelName = N'广发银行' THEN ISNULL(dbd.ServiceFee, 0) ELSE 0 END) AS Fee_Bank_GF,
            SUM(CASE WHEN dc.ChannelName = N'银行' AND dc.SubChannelName = N'中信银行' THEN ISNULL(dbd.ServiceFee, 0) ELSE 0 END) AS Fee_Bank_CITIC,
            SUM(CASE WHEN dc.ChannelName = N'银行' AND dc.SubChannelName = N'广日银联' THEN ISNULL(dbd.ServiceFee, 0) ELSE 0 END) AS Fee_Bank_UnionPay
        FROM dbo.FdCashBak AS b
        INNER JOIN dbo.Dim_Deal_Map AS ddm ON b.ShopId = ddm.ShopId AND b.FdNo = ddm.FdNo COLLATE Chinese_PRC_Stroke_CI_AS
        INNER JOIN dbo.Dim_Channel AS dc ON ddm.ChannelSK = dc.ChannelSK
        LEFT JOIN dbo.Dim_Bank_Deal AS dbd ON b.FdNo = dbd.FdNo COLLATE Chinese_PRC_Stroke_CI_AS -- Join for bank deals
        -- CRITICAL CHANGE: Join to RmCloseInfo to filter by date
        INNER JOIN dbo.RmCloseInfo AS r_filtered
            ON b.InvNo = r_filtered.InvNo
            AND b.Shopid = r_filtered.Shopid
        WHERE
            b.Shopid = @ShopId
            AND r_filtered.WorkDate = CONVERT(VARCHAR(8), @TargetDate, 112) -- Filter RmCloseInfo by WorkDate
        GROUP BY b.InvNo, b.Shopid
    ),
    -- CTE for operational metrics from RmCloseInfo
    OperationalMetrics AS (
        SELECT
            r.InvNo,
            r.Shopid,
            (r.Cash+r.Cash_Targ*0.8+r.Vesa+r.GZ+r.AccOkZD+r.RechargeAccount+r.NoPayed+r.WXPay+r.AliPay+r.MTPay+r.DZPay+r.NMPay+r.[Check]+r.WechatDeposit+r.WechatShopping+r.ReturnAccount) AS TotalRevenue,
            r.WechatOfficialPay,
            r.Numbers,
            r.IsDirectFall
            -- Add other fields from rmcloseinfo as needed
        FROM dbo.RmCloseInfo AS r
        WHERE r.Shopid = @ShopId AND r.CloseDatetime BETWEEN @BeginDate AND @EndDate
    ),
    -- Final aggregation
    FinalMetrics AS (
        SELECT
            SUM(ISNULL(op.TotalRevenue, 0)) AS TotalRevenue,
            SUM(ISNULL(op.WechatOfficialPay, 0)) AS Revenue_OfficialAccount,
            SUM(ISNULL(op.Numbers, 0)) AS BuffetGuestCount,
            SUM(CASE WHEN op.IsDirectFall = 1 THEN op.Numbers ELSE 0 END) AS TotalDirectFallGuests,
            COUNT(DISTINCT op.InvNo) AS TotalBatches,
            
            SUM(ISNULL(cm.Revenue_Meituan_Booking, 0)) AS Revenue_Meituan_Booking,
            SUM(ISNULL(cm.Revenue_Meituan_GroupBuy, 0)) AS Revenue_Meituan_GroupBuy,
            SUM(ISNULL(cm.Revenue_Douyin_Booking, 0)) AS Revenue_Douyin_Booking,
            SUM(ISNULL(cm.Revenue_Douyin_GroupBuy, 0)) AS Revenue_Douyin_GroupBuy,
            SUM(ISNULL(cm.Revenue_Bank_GF, 0)) AS Revenue_Bank_GF,
            SUM(ISNULL(cm.Revenue_Bank_CITIC, 0)) AS Revenue_Bank_CITIC,
            SUM(ISNULL(cm.Revenue_Bank_UnionPay, 0)) AS Revenue_Bank_UnionPay,

            SUM(ISNULL(cm.Fee_Meituan_Booking, 0)) AS Fee_Meituan_Booking,
            SUM(ISNULL(cm.Fee_Meituan_GroupBuy, 0)) AS Fee_Meituan_GroupBuy,
            SUM(ISNULL(cm.Fee_Douyin_Booking, 0)) AS Fee_Douyin_Booking,
            SUM(ISNULL(cm.Fee_Douyin_GroupBuy, 0)) AS Fee_Douyin_GroupBuy,
            SUM(ISNULL(cm.Fee_Bank_GF, 0)) AS Fee_Bank_GF,
            SUM(ISNULL(cm.Fee_Bank_CITIC, 0)) AS Fee_Bank_CITIC,
            SUM(ISNULL(cm.Fee_Bank_UnionPay, 0)) AS Fee_Bank_UnionPay

        FROM OperationalMetrics op
        LEFT JOIN ChannelMetrics cm ON op.InvNo = cm.InvNo AND op.Shopid = cm.Shopid
    )
    -- Update the staging table
    UPDATE dbo.stg_Fact_Daily_Shop_Summary
    SET
        TotalRevenue = ISNULL(fm.TotalRevenue, 0),
        TotalBatches = ISNULL(fm.TotalBatches, 0),
        BuffetGuestCount = ISNULL(fm.BuffetGuestCount, 0),
        TotalDirectFallGuests = ISNULL(fm.TotalDirectFallGuests, 0),
        
        Revenue_Meituan_Booking = ISNULL(fm.Revenue_Meituan_Booking, 0),
        Revenue_Meituan_GroupBuy = ISNULL(fm.Revenue_Meituan_GroupBuy, 0),
        Revenue_Douyin_Booking = ISNULL(fm.Revenue_Douyin_Booking, 0),
        Revenue_Douyin_GroupBuy = ISNULL(fm.Revenue_Douyin_GroupBuy, 0),
        Revenue_Bank_GF = ISNULL(fm.Revenue_Bank_GF, 0),
        Revenue_Bank_CITIC = ISNULL(fm.Revenue_Bank_CITIC, 0),
        Revenue_Bank_UnionPay = ISNULL(fm.Revenue_Bank_UnionPay, 0),
        Revenue_OfficialAccount = ISNULL(fm.Revenue_OfficialAccount, 0),

        Fee_Meituan_Booking = ISNULL(fm.Fee_Meituan_Booking, 0),
        Fee_Meituan_GroupBuy = ISNULL(fm.Fee_Meituan_GroupBuy, 0),
        Fee_Douyin_Booking = ISNULL(fm.Fee_Douyin_Booking, 0),
        Fee_Douyin_GroupBuy = ISNULL(fm.Fee_Douyin_GroupBuy, 0),
        Fee_Bank_GF = ISNULL(fm.Fee_Bank_GF, 0),
        Fee_Bank_CITIC = ISNULL(fm.Fee_Bank_CITIC, 0),
        Fee_Bank_UnionPay = ISNULL(fm.Fee_Bank_UnionPay, 0),

        Revenue_Meituan = ISNULL(fm.Revenue_Meituan_Booking, 0) + ISNULL(fm.Revenue_Meituan_GroupBuy, 0),
        Revenue_Douyin = ISNULL(fm.Revenue_Douyin_Booking, 0) + ISNULL(fm.Revenue_Douyin_GroupBuy, 0),
        Revenue_Bank = ISNULL(fm.Revenue_Bank_GF, 0) + ISNULL(fm.Revenue_Bank_CITIC, 0) + ISNULL(fm.Revenue_Bank_UnionPay, 0),
        Fee_Bank_Total = ISNULL(fm.Fee_Bank_GF, 0) + ISNULL(fm.Fee_Bank_CITIC, 0) + ISNULL(fm.Fee_Bank_UnionPay, 0),

        UpdateTime = GETDATE()
    FROM FinalMetrics fm
    WHERE DateSK = @DateSK AND ShopSK = @ShopSK;

END;
GO