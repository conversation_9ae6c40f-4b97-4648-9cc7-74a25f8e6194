ALTER PROCEDURE dbo.usp_Calculate_Recharge_ForStaging
    @TargetDate DATE,
    @ShopId INT,
    @DateSK INT,
    @ShopSK INT
AS
BEGIN
    SET NOCOUNT ON;

    WITH RechargeCalc AS (
        SELECT
            SUM(ISNULL(ri.<PERSON><PERSON>rge<PERSON><PERSON>, 0)) AS TotalRechargeAmount,
            SUM(CASE WHEN ri.RechargeType = 5 THEN ISNULL(ri.RechargeMoney, 0) ELSE 0 END) AS DailyRecharge_Meituan,
            SUM(CASE WHEN ri.RechargeMoney >= 1 AND ri.RechargeMoney < 500 THEN 1 ELSE 0 END) AS RechargeTier1_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 1 AND ri.RechargeMoney < 500 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier1_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 500 AND ri.RechargeMoney < 1000 THEN 1 ELSE 0 END) AS RechargeTier2_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 500 AND ri.RechargeMoney < 1000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier2_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 1000 AND ri.RechargeMoney < 2000 THEN 1 ELSE 0 END) AS RechargeTier3_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 1000 AND ri.RechargeMoney < 2000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier3_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 2000 AND ri.RechargeMoney < 3000 THEN 1 ELSE 0 END) AS RechargeTier4_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 2000 AND ri.RechargeMoney < 3000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier4_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 3000 AND ri.RechargeMoney < 5000 THEN 1 ELSE 0 END) AS RechargeTier5_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 3000 AND ri.RechargeMoney < 5000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier5_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 5000 AND ri.RechargeMoney < 10000 THEN 1 ELSE 0 END) AS RechargeTier6_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 5000 AND ri.RechargeMoney < 10000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier6_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 10000 AND ri.RechargeMoney < 20000 THEN 1 ELSE 0 END) AS RechargeTier7_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 10000 AND ri.RechargeMoney < 20000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier7_TotalAmount,
            SUM(CASE WHEN ri.RechargeMoney >= 20000 THEN 1 ELSE 0 END) AS RechargeTier8_BatchCount,
            SUM(CASE WHEN ri.RechargeMoney >= 20000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier8_TotalAmount
        FROM
            mims.dbo.RechargedataInfo AS ri
        WHERE
            CONVERT(DATE, ri.RechargeTime) = @TargetDate
            AND ri.RechargeMoney > 0
            AND ri.RechargeShopId = @ShopId
    )
    UPDATE dbo.stg_Fact_Daily_Shop_Summary
    SET
        DailyRecharge_Total = ISNULL(rc.TotalRechargeAmount, 0),
        DailyRecharge_Meituan = ISNULL(rc.DailyRecharge_Meituan, 0),
        DailyRecharge_Meituan_Ratio = CASE WHEN ISNULL(rc.TotalRechargeAmount, 0) = 0 THEN 0 ELSE ISNULL(rc.DailyRecharge_Meituan, 0) / rc.TotalRechargeAmount END,
        RechargeTier1_BatchCount = rc.RechargeTier1_BatchCount,
        RechargeTier1_TotalAmount = rc.RechargeTier1_TotalAmount,
        RechargeTier2_BatchCount = rc.RechargeTier2_BatchCount,
        RechargeTier2_TotalAmount = rc.RechargeTier2_TotalAmount,
        RechargeTier3_BatchCount = rc.RechargeTier3_BatchCount,
        RechargeTier3_TotalAmount = rc.RechargeTier3_TotalAmount,
        RechargeTier4_BatchCount = rc.RechargeTier4_BatchCount,
        RechargeTier4_TotalAmount = rc.RechargeTier4_TotalAmount,
        RechargeTier5_BatchCount = rc.RechargeTier5_BatchCount,
        RechargeTier5_TotalAmount = rc.RechargeTier5_TotalAmount,
        RechargeTier6_BatchCount = rc.RechargeTier6_BatchCount,
        RechargeTier6_TotalAmount = rc.RechargeTier6_TotalAmount,
        RechargeTier7_BatchCount = rc.RechargeTier7_BatchCount,
        RechargeTier7_TotalAmount = rc.RechargeTier7_TotalAmount,
        RechargeTier8_BatchCount = rc.RechargeTier8_BatchCount,
        RechargeTier8_TotalAmount = rc.RechargeTier8_TotalAmount
    FROM RechargeCalc rc
    WHERE DateSK = @DateSK AND ShopSK = @ShopSK;
END
