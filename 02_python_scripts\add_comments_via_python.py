import pyodbc

# Connection details
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'

# Comments to be added
comments = {
    'TABLE': '存放按“天”汇总的全局性、总结性指标。',
    'ShopSummarySK': '代理键。',
    'DateSK': '外键，关联到 Dim_Date。',
    'ShopSK': '外键，关联到 Dim_Shop。',
    'TotalRevenue': '全天总营业额。',
    'DayTimeRevenue': '白天档总营业额 (由各时段数据汇总)。',
    'NightTimeRevenue': '晚上档总营业额 (由各时段数据汇总)。',
    'TotalBatches': '全天总批次数。',
    'BuffetGuestCount': '全天自助餐总人数。',
    'TotalDirectFallGuests': '全天直落总人数。',
    'ComplimentaryBatches': '全天招待总批次数。',
    'ComplimentaryRevenue': '全天招待总金额。',
    'PrivilegeBooking_Count_0Yuan': '0元特权预约的执行次数。',
    'PrivilegeBooking_Count_5Yuan': '5元特权预约的执行次数。',
    'PrivilegeBooking_Count_10Yuan': '10元特权预约的执行次数。',
    'PrivilegeBooking_Count_15Yuan': '15元特权预约的执行次数。',
    'Fee_Meituan_Booking': '支付给美团的预约类总手续费。',
    'Fee_Meituan_GroupBuy': '支付给美团的团购类总手续费。',
    'Fee_Douyin_Booking': '支付给抖音的预约类总手续费。',
    'Fee_Douyin_GroupBuy': '支付给抖音的团购类总手续费。',
    'Fee_Bank_GF': '支付给广发银行的总手续费。',
    'Fee_Bank_CITIC': '支付给中信银行的总手续费。',
    'Fee_Bank_UnionPay': '支付给银联的总手续费。',
    'Night_FreeMeal_Batches': '夜间自由餐批次数。',
    'Night_FreeMeal_Revenue': '夜间自由餐营收。',
    'Night_Buyout_Batches': '夜间买断批次数。',
    'Night_DrinkPackage_Batches': '夜间畅饮套餐批次数。',
    'Night_FreeConsumption_Batches': '夜间自由消费套餐批次数。',
    'Night_RoomFee_Batches': '夜间房费批次数。',
    'Night_Other_Batches': '夜间其他批次数。',
    'CreateTime': '创建时间。',
    'UpdateTime': '最后更新时间。',
    'OperatorId': '操作人ID。',
    'DailyRecharge_Total': '当天充值总额 (线上+线下)。',
    'DailyRecharge_Meituan': '当天新美大渠道充值总额。',
    'DailyRecharge_Meituan_Ratio': '当天新美大充值占比。',
    'RechargeTier1_BatchCount': '充值档位(1-499元)批次数。',
    'RechargeTier1_TotalAmount': '充值档位(1-499元)总金额。',
    'RechargeTier2_BatchCount': '充值档位(500-999元)批次数。',
    'RechargeTier2_TotalAmount': '充值档位(500-999元)总金额。',
    'RechargeTier3_BatchCount': '充值档位(1000-1999元)批次数。',
    'RechargeTier3_TotalAmount': '充值档位(1000-1999元)总金额。',
    'RechargeTier4_BatchCount': '充值档位(2000-2999元)批次数。',
    'RechargeTier4_TotalAmount': '充值档位(2000-2999元)总金额。',
    'RechargeTier5_BatchCount': '充值档位(3000-4999元)批次数。',
    'RechargeTier5_TotalAmount': '充值档位(3000-4999元)总金额。',
    'RechargeTier6_BatchCount': '充值档位(5000-9999元)批次数。',
    'RechargeTier6_TotalAmount': '充值档位(5000-9999元)总金额。',
    'RechargeTier7_BatchCount': '充值档位(10000-19999元)批次数。',
    'RechargeTier7_TotalAmount': '充值档位(10000-19999元)总金额。',
    'RechargeTier8_BatchCount': '充值档位(20000元及以上)批次数。',
    'RechargeTier8_TotalAmount': '充值档位(20000元及以上)总金额。',
}

# SQL templates
DROP_TABLE_SQL = """
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('dbo.Fact_Daily_Shop_Summary') AND minor_id = OBJECT_ID('dbo.Fact_Daily_Shop_Summary') AND name = N'MS_Description')
BEGIN
    EXEC sp_dropextendedproperty @name = N'MS_Description', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Fact_Daily_Shop_Summary';
END
"""
ADD_TABLE_SQL = """
EXEC sp_addextendedproperty @name = N'MS_Description', @value = ?, @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE',  @level1name = N'Fact_Daily_Shop_Summary';
"""

DROP_COLUMN_SQL = """
IF EXISTS (SELECT 1 FROM sys.extended_properties WHERE major_id = OBJECT_ID('dbo.Fact_Daily_Shop_Summary') AND minor_id = COLUMNPROPERTY(OBJECT_ID('dbo.Fact_Daily_Shop_Summary'), ?, 'ColumnId') AND name = N'MS_Description')
BEGIN
    EXEC sp_dropextendedproperty @name = N'MS_Description', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Fact_Daily_Shop_Summary', @level2type = N'COLUMN', @level2name = ?;
END
"""
ADD_COLUMN_SQL = """
EXEC sp_addextendedproperty @name = N'MS_Description', @value = ?, @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Fact_Daily_Shop_Summary', @level2type = N'COLUMN', @level2name = ?;
"""

cnxn = None
try:
    cnxn = pyodbc.connect(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;')
    cursor = cnxn.cursor()
    print("Connection successful. Starting to add/update comments...")

    # Update table comment
    table_comment = comments.pop('TABLE')
    cursor.execute(DROP_TABLE_SQL)
    cursor.execute(ADD_TABLE_SQL, table_comment)
    print(f"Updated comment for TABLE: Fact_Daily_Shop_Summary")

    # Update column comments
    for col_name, comment_text in comments.items():
        cursor.execute(DROP_COLUMN_SQL, col_name, col_name)
        cursor.execute(ADD_COLUMN_SQL, comment_text, col_name)
        print(f"Updated comment for COLUMN: {col_name}")

    cnxn.commit()
    print("\nTransaction committed successfully! All comments are updated.")

except Exception as e:
    if cnxn:
        cnxn.rollback()
    print(f"An error occurred: {e}")

finally:
    if cnxn:
        cnxn.close()
    print("Connection closed.")