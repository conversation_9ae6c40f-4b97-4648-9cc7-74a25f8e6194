
import pyodbc
import pandas as pd

# --- Configuration ---
DB_SERVER = '***********'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
OUTPUT_SQL_FILE = 'C:/Users/<USER>/CascadeProjects/KTV_Data_Analysis/01_sql_scripts/maintenance/generated_guest_count_bulk_update.sql'
SHOP_ID = 3

def generate_guest_count_update_script():
    """
    Generates a bulk SQL UPDATE script with hardcoded values for TotalDirectFallGuests.
    """
    print("开始生成直落人数的UPDATE脚本...")
    cnxn = None
    try:
        # 1. Connect to the database
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
        cnxn = pyodbc.connect(conn_str)
        print("数据库连接成功。")

        # 2. Calculate the correct daily guest counts
        calculation_sql = f"""
        SELECT 
            WorkDate,
            SUM(ISNULL(Numbers, 0)) AS TotalGuests
        FROM 
            dbo.RmCloseInfo
        WHERE 
            IsDirectFall = 1
            AND ShopId = {SHOP_ID}
            AND WorkDate BETWEEN '20250801' AND '20250831'
        GROUP BY
            WorkDate
        ORDER BY
            WorkDate;
        """
        print("正在计算每日的直落总人数...")
        correct_counts_df = pd.read_sql(calculation_sql, cnxn)
        print(f"计算完成，共得到 {len(correct_counts_df)} 天的数据。")

        # 3. Generate hardcoded UPDATE statements
        all_update_statements = [
            "USE operatedata;",
            "GO",
            f"-- Bulk update for TotalDirectFallGuests for ShopID={SHOP_ID} for August 2025",
            "PRINT 'Starting hardcoded bulk update for guest counts...';",
            "GO"
        ]

        for index, row in correct_counts_df.iterrows():
            work_date = row['WorkDate']
            total_guests = row['TotalGuests']
            
            update_sql = (
                f"UPDATE dbo.FullDailyReport_Header "
                f"SET TotalDirectFallGuests = {total_guests} "
                f"WHERE ReportDate = '{work_date}' AND ShopID = {SHOP_ID};"
            )
            all_update_statements.append(update_sql)

        all_update_statements.append("PRINT 'Guest count bulk update complete.';")
        all_update_statements.append("GO")

        # 4. Write to file
        with open(OUTPUT_SQL_FILE, 'w', encoding='utf-8') as f:
            f.write('\n'.join(all_update_statements))
        
        print(f"\n成功! 包含 {len(correct_counts_df)} 条 UPDATE 语句的脚本已生成。")
        print(f"文件路径: {OUTPUT_SQL_FILE}")

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    generate_guest_count_update_script()
