
SET NOCOUNT ON;

-- KTV Daily Recharge Summary Report by Tiers
-- This query aggregates recharge data by shop and day, breaking it down into 8 tiers.

SELECT
    CONVERT(date, RechargeTime) AS WorkDate,
    RechargeShopId AS ShopId,

    -- Total Summary
    COUNT(*) AS TotalBatches,
    SUM(RechargeMoney) AS TotalRechargeAmount,

    -- Tier 1: 1-499
    SUM(CASE WHEN RechargeMoney >= 1 AND RechargeMoney < 500 THEN 1 ELSE 0 END) AS RechargeTier1_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 1 AND RechargeMoney < 500 THEN RechargeMoney ELSE 0 END) AS RechargeTier1_TotalAmount,

    -- Tier 2: 500-999
    SUM(CASE WHEN RechargeMoney >= 500 AND RechargeMoney < 1000 THEN 1 ELSE 0 END) AS RechargeTier2_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 500 AND RechargeMoney < 1000 THEN RechargeMoney ELSE 0 END) AS RechargeTier2_TotalAmount,

    -- Tier 3: 1000-1999
    SUM(CASE WHEN RechargeMoney >= 1000 AND RechargeMoney < 2000 THEN 1 ELSE 0 END) AS RechargeTier3_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 1000 AND RechargeMoney < 2000 THEN RechargeMoney ELSE 0 END) AS RechargeTier3_TotalAmount,

    -- Tier 4: 2000-2999
    SUM(CASE WHEN RechargeMoney >= 2000 AND RechargeMoney < 3000 THEN 1 ELSE 0 END) AS RechargeTier4_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 2000 AND RechargeMoney < 3000 THEN RechargeMoney ELSE 0 END) AS RechargeTier4_TotalAmount,

    -- Tier 5: 3000-4999
    SUM(CASE WHEN RechargeMoney >= 3000 AND RechargeMoney < 5000 THEN 1 ELSE 0 END) AS RechargeTier5_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 3000 AND RechargeMoney < 5000 THEN RechargeMoney ELSE 0 END) AS RechargeTier5_TotalAmount,

    -- Tier 6: 5000-9999
    SUM(CASE WHEN RechargeMoney >= 5000 AND RechargeMoney < 10000 THEN 1 ELSE 0 END) AS RechargeTier6_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 5000 AND RechargeMoney < 10000 THEN RechargeMoney ELSE 0 END) AS RechargeTier6_TotalAmount,

    -- Tier 7: 10000-19999
    SUM(CASE WHEN RechargeMoney >= 10000 AND RechargeMoney < 20000 THEN 1 ELSE 0 END) AS RechargeTier7_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 10000 AND RechargeMoney < 20000 THEN RechargeMoney ELSE 0 END) AS RechargeTier7_TotalAmount,

    -- Tier 8: 20000+
    SUM(CASE WHEN RechargeMoney >= 20000 THEN 1 ELSE 0 END) AS RechargeTier8_BatchCount,
    SUM(CASE WHEN RechargeMoney >= 20000 THEN RechargeMoney ELSE 0 END) AS RechargeTier8_TotalAmount

FROM
    mims.dbo.RechargedataInfo
WHERE
    RechargeMoney > 0
    AND RechargeTime >= '2025-08-01' -- Limit to recent data for preview
GROUP BY
    CONVERT(date, RechargeTime),
    RechargeShopId
ORDER BY
    WorkDate DESC,
    ShopId;
