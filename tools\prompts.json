{"sql_fix": {"template": "请检查以下SQL代码中的语法错误，并提供修正后的版本以及详细的解释。代码如下：\n---\n{0}\n---", "description": "发送一段SQL代码，让AI检查并修正错误。"}, "code_review": {"template": "请帮我Review以下代码，从代码风格、可读性、潜在bug和性能优化几个角度给出建议。代码语言是{0}，代码如下：\n---\n{1}\n---", "description": "发送一段代码，让AI进行全面的代码审查。"}, "commit_msg": {"template": "请根据以下的git diff信息，为我生成一条符合Conventional Commits规范的git commit消息。diff信息如下：\n---\n{0}\n---", "description": "根据git diff生成标准的commit消息。"}}