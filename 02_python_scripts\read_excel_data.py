
import pandas as pd
import os

def read_excel_file():
    """
    Reads data from a specified Excel file using pandas and prints the content.
    """
    # Construct the absolute path to the file
    # Assuming the script is in '02_python_scripts' and the Excel file is in the root
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        file_path = os.path.join(project_root, '营业数据统计平台开发需求 (1).xlsx')

        print(f"正在读取文件: {file_path}\n")

        # Read the Excel file. By default, it reads the first sheet.
        # pandas uses openpyxl as its engine for .xlsx files.
        df = pd.read_excel(file_path)

        print("--- Excel 文件内容 ---")
        print(df.to_string())
        print("\n--- 读取完毕 ---")

    except FileNotFoundError:
        print(f"错误: 文件未找到，请确认路径是否正确: {file_path}")
    except Exception as e:
        print(f"发生错误: {e}")
        print("\n请确保您已经安装了pandas和openpyxl库。")
        print("您可以使用此命令安装: pip install pandas openpyxl")

if __name__ == '__main__':
    read_excel_file()
