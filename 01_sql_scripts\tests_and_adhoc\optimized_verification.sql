SET NOCOUNT ON;
PRINT 'Optimized Verification Script Started.';
PRINT '------------------------------------';

-- Step 1: Create a temp table and populate it with all unique keys from the remote source.
PRINT 'Step 1: Fetching all unique keys from remote source into a local temp table...';
IF OBJECT_ID('tempdb..#RemoteKeys') IS NOT NULL DROP TABLE #RemoteKeys;
CREATE TABLE #RemoteKeys (Ikey VARCHAR(36) PRIMARY KEY);

INSERT INTO #RemoteKeys (Ikey)
SELECT DISTINCT T.Ikey
FROM (
    SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory
    UNION ALL
    SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory_bak
) AS T;
PRINT '-> Done. ' + CAST(@@ROWCOUNT AS VARCHAR) + ' unique remote keys fetched.';
PRINT '';

-- Step 2: Find 10 keys in the local table that are NOT in our local temp table of remote keys.
PRINT 'Step 2: Finding 10 sample "extra" keys by comparing local tables...';
DECLARE @SampleKeys TABLE (Ikey VARCHAR(36));

INSERT INTO @SampleKeys (Ikey)
SELECT TOP 10 oci.Ikey
FROM operatedata.dbo.opencacheinfo oci
WHERE NOT EXISTS (
    SELECT 1
    FROM #RemoteKeys rk
    WHERE rk.Ikey = oci.Ikey
);

IF NOT EXISTS (SELECT 1 FROM @SampleKeys)
BEGIN
    PRINT '-> No "extra" keys found. This is unexpected.';
    DROP TABLE #RemoteKeys;
    RETURN;
END
PRINT '-> Done. Found sample keys. They are listed below:';
SELECT Ikey AS SampleExtraKey FROM @SampleKeys;
PRINT '';

-- Step 3: Re-verify these 10 keys against the remote source, just to be 100% certain.
PRINT 'Step 3: Double-checking the sample keys against the remote openhistory table...';

SELECT oh.Ikey
FROM cloudRms2019.rms2019.dbo.openhistory oh
INNER JOIN @SampleKeys sk ON oh.Ikey = sk.Ikey;

IF @@ROWCOUNT = 0
BEGIN
    PRINT '-> Verification Complete: As expected, none of the sample keys were found in the remote openhistory table.';
END
ELSE
BEGIN
    PRINT '-> Verification Complete: UNEXPECTED RESULT! Some keys were found.';
END

-- Cleanup
DROP TABLE #RemoteKeys;
PRINT '------------------------------------';
PRINT 'Script Finished.';
