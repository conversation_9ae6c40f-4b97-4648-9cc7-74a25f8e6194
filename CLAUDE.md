# CLAUDE.md

此文件为Claude Code (claude.ai/code)在本代码库中工作时提供指导。

## 项目概述

这是一个KTV（卡拉OK）业务数据分析系统，旨在分析预订、结账和交易数据。该系统能够检测时间段重叠，识别直落客户（客人续单），执行渠道分析，并为KTV运营生成业务报告。

## 架构

### 数据库基础设施
- **总部服务器 (192.168.2.5)**: 包含operatedata（运营数据库）、mims（会员数据库）、rms2019（同步目标数据库）和本地dbfood数据库
- **分店服务器 (例如：193.112.2.229)**: 拥有自己的rms2019（同步源）和dbfood数据库
- **链接服务器**: 用于通过'cloudRms2019'等名称将分店rms2019数据库连接到总部
- **MySQL服务器**: 用于微信相关数据的外部MySQL数据库，位于yy.tang-hui.com.cn

### 核心数据流
1. **数据同步**: 通过SQL Agent作业从分店rms2019每日同步到总部rms2019
2. **数据处理**: SQL存储过程转换和分析运营数据
3. **报表生成**: Python和SQL脚本生成全面的业务报告
4. **银行交易集成**: 银行交易数据集成，用于全面的财务分析

### 关键组件
- **存储过程**: 时间段分析、直落客户检测、报表生成
- **SQL Agent作业**: 自动化数据同步和报表生成
- **Python脚本**: 数据处理、ETL操作和报表生成
- **维度建模**: 用于分析的事实/维度表数据仓库设计

### 数据仓库结构
- **维度表**: Dim_Date, Dim_Shop, Dim_TimeSlot, Dim_Bank_Deal
- **事实表**: Fact_Daily_TimeSlot_Summary（粒度：门店 x 日期 x 时间段）, Fact_Daily_Shop_Summary（粒度：门店 x 日期）, Fact_Deal_Redemption
- **ETL流程**: 每日执行usp_Populate_Analytics_Daily_Summary以填充事实表

## 目录结构

```
├── 00_deployment/          # 生产部署脚本
├── 01_sql_scripts/         # SQL脚本（核心逻辑）
│   ├── reports/           # 报表生成过程
│   ├── maintenance/       # 数据库维护脚本
│   └── ...                # 核心表创建和ETL逻辑
├── 02_python_scripts/     # Python数据处理脚本
├── 03_reports/           # 生成的报表和文档
├── 04_database_exports/   # 数据库模式导出
├── 05_documentation/     # 系统文档
└── archive/              # 历史和已弃用文件
```

## 业务逻辑

### 房间状态代码
- A=结账, B=坏房, C=续单, D=清洁
- E=空房, H=预转, L=慢带, U=占用
- R=留房, W=派房, V=微信, F=预结

### 核心分析
- **时间段重叠检测**: 识别与业务时间段重叠的客户消费时段
- **直落客户识别**: 区分真正的直落客户（客人续单）和延时付款
- **渠道分析**: 按来源（K+、美团、抖音、特权预约）对客户进行分类
- **收入分析**: 基于时间的收入统计，区分白天和晚上时段

### 数据处理规则
- **有效预订**: 通过isdelete字段过滤（有效=0）
- **客户计数**: opencacheinfo/openhistory中的Source字段为Numbers（不在FdCashBak中）
- **时间段统计**: 使用Beg_Key和End_Key字段
- **工作日期计算**: 运营数据的营业日从上午9:00开始

## 开发命令

### 环境设置
```bash
pip install -r requirements.txt
```

### SQL执行
对复杂查询使用`sqlcmd`：
```bash
sqlcmd -S server_name -d database_name -i script.sql -o output.csv
```

测试存储过程：
```sql
EXEC usp_GenerateDynamicUnifiedDailyReport @BeginDate='2025-07-01', @EndDate='2025-07-31', @ShopId=11
```

### Python脚本执行
```bash
python script_name.py
```

### 测试和调试
- **测试数据库连接**: 
  - `python test_193_connection.py` (测试到分店RMS服务器的连接)
  - `python test_mysql_connection.py` (测试到MySQL服务器的连接)
- **验证数据同步**: `python check_sync_job_status.py`
- **调试存储过程**: `python debug_sp_execution.py`

### 数据库操作
- **测试连接**: `test_193_connection.py`, `test_mysql_connection.py`
- **生成模式**: `generate_db_inventory.py`, `get_table_schemas_from_both_servers.py`
- **数据同步**: `daily_sync_transactions.py`, `populate_fact_deal_redemption.py`
- **银行交易导入**: `import_bank_deals_from_json_v5.py`, `final_import_bank_deals.py`

### 常见SQL Agent作业
- `RMS_Daily_Data_Sync_to_HQ`: 主同步作业
- `BankDeal_Daily_ETL_Job`: 银行交易数据ETL作业

## 技术指南

### 文件组织
- 新文件遵循基于类型的编号目录结构
- 遗留文件移至`archive/`目录
- Python脚本在`02_python_scripts/`中，SQL脚本在`01_sql_scripts/`中

### 数据库访问
- **分店总部同步**: 使用链接服务器`cloudRms2019.rms2019.dbo.table_name`
- **本地处理**: 直接连接到本地数据库
- **连接测试**: 在数据操作前始终验证连接

### 代码标准
- 所有业务逻辑使用中文和UTF-8编码
- 当脚本修改多次失败时，创建新文件而不是继续修改现有文件
- 存储过程遵循命名约定：`usp_Generate*`用于报表，`usp_Sync_*`用于数据同步
- 对复杂SQL执行使用sqlcmd的`-i`参数以避免引号转义问题

### 关键存储过程
- `usp_GenerateDynamicUnifiedDailyReport`: 主报表生成
- `usp_Sync_RMS_DailyOpenData`: 每日开台数据同步
- `usp_Sync_RMS_DailyBookData`: 每日预订数据同步
- `usp_Populate_Analytics_Daily_Summary`: 填充数据仓库事实表
- `RMS_Daily_Data_Sync_to_HQ`: 主同步作业

### 常见问题
- 如果每日同步作业缺失，使用`00_deployment/04_create_sql_agent_job.sql`重新创建
- 核心消费数据在本地`operatedata.dbo.FdCashBak`中，不在远程`dbfood`中
- 有效预订过滤需要`isdelete = 0`条件

always response in Chinese utf-8
# 开发指南

## 哲学理念

### 核心信念

- **增量进展优于大爆炸式变更** - 小规模变更，确保能编译并通过测试
- **从现有代码中学习** - 在实现前先研究和规划
- **务实胜过教条** - 适应项目实际情况
- **明确意图胜过聪明代码** - 保持简单明了

### 简单性意味着

- 每个函数/类只承担单一职责
- 避免过早抽象
- 不使用 clever tricks - 选择简单的解决方案
- 如果需要解释，说明太复杂了

## 过程

### 1. 规划与分阶段

将复杂工作分解为3-5个阶段。在`IMPLEMENTATION_PLAN.md`中记录：

```markdown
## 阶段 N: [名称]
**目标**: [具体交付物]
**成功标准**: [可测试的结果]
**测试**: [具体测试用例]
**状态**: [未开始|进行中|已完成]
```
- 随着进展更新状态
- 所有阶段完成后移除文件

### 2. 实现流程

1. **理解** - 研究代码库中的现有模式
2. **测试** - 先写测试（红色）
3. **实现** - 编写最少代码以通过测试（绿色）
4. **重构** - 在测试通过的情况下清理代码
5. **提交** - 使用清晰的提交信息关联计划

### 3. 遇到困难时（尝试3次后）

**重要**: 每个问题最多尝试3次，然后停止。

1. **记录失败原因**:
   - 你尝试了什么
   - 具体的错误信息
   - 你认为失败的原因

2. **研究替代方案**:
   - 寻找2-3个类似实现
   - 注意使用了哪些不同的方法

3. **质疑基础**:
   - 这是正确的抽象层次吗？
   - 能否拆分成更小的问题？
   - 是否有完全不同的简单方法？

4. **尝试不同角度**:
   - 不同的库/框架功能？
   - 不同的架构模式？
   - 移除抽象而不是添加？

## 技术标准

### 架构原则

- **组合优于继承** - 使用依赖注入
- **接口优于单例** - 便于测试和灵活性
- **明确优于隐式** - 清晰的数据流和依赖关系
- **尽可能测试驱动** - 永不禁用测试，而是修复它们

### 代码质量

- **每次提交必须**:
  - 成功编译
  - 通过所有现有测试
  - 包含新功能的测试
  - 遵循项目格式化/代码检查规范

- **提交前**:
  - 运行格式化工具/代码检查
  - 自我审查变更
  - 确保提交信息解释"为什么"

### 错误处理

- 快速失败并提供描述性信息
- 包含调试上下文
- 在适当级别处理错误
- 永不静默吞掉异常

## 决策框架

当存在多个有效方法时，基于以下标准选择：

1. **可测试性** - 我能轻松测试吗？
2. **可读性** - 6个月后有人能理解吗？
3. **一致性** - 这是否符合项目模式？
4. **简单性** - 这是最简单有效的解决方案吗？
5. **可逆性** - 以后更改有多难？

## 项目集成

### 学习代码库

- 寻找3个类似的功能/组件
- 识别通用模式和约定
- 尽可能使用相同的库/工具
- 遵循现有的测试模式

### 工具

- 使用项目现有的构建系统
- 使用项目的测试框架
- 使用项目的格式化工具/代码检查设置
- 没有充分理由不要引入新工具

## 质量门禁

### 完成的定义

- [ ] 编写并通过了测试
- [ ] 代码遵循项目约定
- [ ] 没有代码格式化/检查警告
- [ ] 提交信息清晰
- [ ] 实现与计划匹配
- [ ] 没有无问题编号的TODO

### 测试指南

- 测试行为，而不是实现
- 尽可能每个测试一个断言
- 清晰的测试名称描述场景
- 使用现有的测试工具/助手
- 测试应该是确定性的

## 重要提醒

**永不**:
- 使用 `--no-verify` 绕过提交钩子
- 禁用测试而不是修复它们
- 提交无法编译的代码
- 做出假设 - 用现有代码验证

**始终**:
- 增量提交可工作的代码
- 随着进展更新计划文档
- 从现有实现中学习
- 尝试3次失败后停止并重新评估
- 保存一些你尝试过的，觉得重要的核心使用方式，不论是代码相关还是sql相关或者命令行，到memory里面