import pandas as pd
import os

# 定义Excel文件路径
excel_files = [
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\储值套餐统计表.xlsx',
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\05_documentation\附件：客服专员提成测算.xlsx',
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\05_documentation\客服专员提成报表--模版.xlsx',
    r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\营业数据-多门店-营业报表详细统计.xlsx'
]

def read_excel_content(file_path):
    """读取Excel文件内容"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None
            
        print(f"\n正在读取文件: {file_path}")
        
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表名称: {excel_file.sheet_names}")
        
        # 读取每个工作表的内容
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- 工作表: {sheet_name} ---")
            # 使用openpyxl引擎读取Excel文件
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            print(f"行列数: {df.shape}")
            print("前10行数据:")
            print(df.head(10))
            print("\n列名:")
            print(list(df.columns))
            print("\n数据类型:")
            print(df.dtypes)
            
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")

# 读取所有Excel文件
print("开始读取Excel文件...")
for file_path in excel_files:
    read_excel_content(file_path)
    print("-" * 50)