
'''
This script connects to the SQL Server database and retrieves the schema information 
for the source tables (openhistory, openhistory_bak) and the target table (opencacheinfo).
'''
import pyodbc

# Connection details from memory
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Tables to analyze
TABLES_TO_ANALYZE = [
    "cloudRms2019.rms2019.dbo.openhistory",
    "cloudRms2019.rms2019.dbo.openhistory_bak",
    "operatedata.dbo.opencacheinfo"
]

def get_table_schema(cursor, table_name):
    """Fetches and prints schema for a given table."""
    print(f"--- Analyzing Schema for: {table_name} ---")
    try:
        cursor.execute(f"sp_help '{table_name}'")
        
        # Print column info
        print("\n[Columns]")
        columns = cursor.fetchall()
        if columns:
            # Assuming the first result set from sp_help is the column list
            # We need to advance to the next result set to get column details
            # This is a bit of a simplification, as sp_help returns multiple result sets.
            # For robust parsing, one would typically loop through nextset().
            
            # Let's just try to get the column names from the description
            cols_description = [desc[0] for desc in cursor.description]
            print("\t".join(cols_description))
            
            # Re-fetch might be needed depending on driver, but let's try printing first
            # For simplicity, let's just print what we have. A more robust solution
            # might be needed if the output is not as expected.
            # The raw output is often sufficient for initial analysis.
            for row in columns:
                print("\t".join(str(x) for x in row))

        # We can add more details here if needed, e.g., for indexes, constraints
        print(f"--- End of Schema for: {table_name} ---\n")

    except pyodbc.Error as ex:
        sqlstate = ex.args[0]
        print(f"ERROR fetching schema for {table_name}: {sqlstate}")
        print(ex)
    print("\n" + "="*80 + "\n")

def main():
    """Main function to connect to DB and analyze schemas."""
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        for table in TABLES_TO_ANALYZE:
            get_table_schema(cursor, table)
            
    except pyodbc.Error as ex:
        print(f"Database connection failed: {ex}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("Connection closed.")

if __name__ == "__main__":
    main()
