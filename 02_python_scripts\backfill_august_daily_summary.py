import pyodbc
from datetime import date, timedelta

# --- Configuration ---
db_connection_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
start_date = date(2025, 8, 1)
end_date = date(2025, 8, 31)

# --- SQL Queries ---
sql_get_active_shops = "SELECT ShopID FROM dbo.Dim_Shop WHERE IsActive = 1 ORDER BY ShopID"
sql_run_etl = "EXEC dbo.usp_Populate_Fact_Daily_Shop_Summary @TargetDate = ?, @ShopId = ?"

# --- Execution ---
cnxn = None
cursor = None
try:
    cnxn = pyodbc.connect(db_connection_str)
    cursor = cnxn.cursor()

    # 1. Get active shops
    print("Fetching active shops...")
    cursor.execute(sql_get_active_shops)
    active_shops = [row.ShopID for row in cursor.fetchall()]
    print(f"Found {len(active_shops)} active shops: {active_shops}")

    # 2. Loop through dates and shops to run ETL
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        print(f"--- Processing Date: {date_str} ---")
        for shop_id in active_shops:
            try:
                print(f"  Running ETL for ShopID: {shop_id}...", end='')
                cursor.execute(sql_run_etl, date_str, shop_id)
                # Stored procedures with SET NOCOUNT ON might not return a rowcount.
                # We commit after each successful execution.
                cnxn.commit()
                print(f" Done.")
            except Exception as e:
                print(f"  ERROR processing ShopID: {shop_id} for {date_str}. Error: {e}")
                cnxn.rollback()

        current_date += timedelta(days=1)

    print("\n--- August backfill process completed. ---")

except Exception as e:
    print(f"An error occurred during the process: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
