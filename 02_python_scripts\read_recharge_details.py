import pandas as pd
import os

# 文件路径
file_name = '储值套餐统计表.xlsx'
file_path = os.path.join(os.getcwd(), file_name)

def read_recharge_details():
    """
    专门读取充值明细表的内容
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：在项目根目录下找不到文件 '{file_name}'")
            print(f"完整路径: {file_path}")
            return

        # 读取Excel文件中的第二个工作表（充值明细表）
        print(f"正在读取文件: {file_path}")
        df = pd.read_excel(file_path, sheet_name='储值明细表', engine='openpyxl')
        
        print(f"\n--- 充值明细表 ---")
        print(f"行列数: {df.shape}")
        print("所有数据:")
        print(df.to_string())
        print("\n列名:")
        print(list(df.columns))
        
    except Exception as e:
        print(f"读取或处理文件时发生错误: {e}")

if __name__ == '__main__':
    read_recharge_details()