USE operatedata;
GO

-- Drop the existing procedure if it exists
IF OBJECT_ID('dbo.usp_Update_Fact_Daily_Shop_Summary_Recharge', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Update_Fact_Daily_Shop_Summary_Recharge;
END
GO

-- Create the updated procedure
CREATE PROCEDURE dbo.usp_Update_Fact_Daily_Shop_Summary_Recharge
    @TargetDate DATE
AS
BEGIN
    -- ====================================================================================
    -- Author:      Gemini
    -- Create date: 2025-08-25
    -- Description: Calculates recharge summary data from mims.RechargedataInfo
    --              and updates the Fact_Daily_Shop_Summary table for a specific day.
    --              VERSION 2: Filters for active shops only (ds.IsActive = 1).
    -- ====================================================================================
    SET NOCOUNT ON;

    IF OBJECT_ID('tempdb..#RechargeSummary') IS NOT NULL
    BEGIN
        DROP TABLE #RechargeSummary;
    END

    -- 1. Aggregate data from source, now filtering for active shops.
    SELECT
        ri.RechargeShopId AS ShopId,
        SUM(ri.RechargeMoney) AS TotalRechargeAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 1 AND ri.RechargeMoney < 500 THEN 1 ELSE 0 END) AS RechargeTier1_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 1 AND ri.RechargeMoney < 500 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier1_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 500 AND ri.RechargeMoney < 1000 THEN 1 ELSE 0 END) AS RechargeTier2_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 500 AND ri.RechargeMoney < 1000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier2_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 1000 AND ri.RechargeMoney < 2000 THEN 1 ELSE 0 END) AS RechargeTier3_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 1000 AND ri.RechargeMoney < 2000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier3_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 2000 AND ri.RechargeMoney < 3000 THEN 1 ELSE 0 END) AS RechargeTier4_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 2000 AND ri.RechargeMoney < 3000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier4_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 3000 AND ri.RechargeMoney < 5000 THEN 1 ELSE 0 END) AS RechargeTier5_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 3000 AND ri.RechargeMoney < 5000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier5_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 5000 AND ri.RechargeMoney < 10000 THEN 1 ELSE 0 END) AS RechargeTier6_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 5000 AND ri.RechargeMoney < 10000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier6_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 10000 AND ri.RechargeMoney < 20000 THEN 1 ELSE 0 END) AS RechargeTier7_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 10000 AND ri.RechargeMoney < 20000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier7_TotalAmount,
        SUM(CASE WHEN ri.RechargeMoney >= 20000 THEN 1 ELSE 0 END) AS RechargeTier8_BatchCount,
        SUM(CASE WHEN ri.RechargeMoney >= 20000 THEN ri.RechargeMoney ELSE 0 END) AS RechargeTier8_TotalAmount
    INTO #RechargeSummary
    FROM
        mims.dbo.RechargedataInfo AS ri
    -- The crucial JOIN to filter by active shops
    JOIN
        dbo.Dim_Shop AS ds ON ri.RechargeShopId = ds.ShopID
    WHERE
        CONVERT(DATE, ri.RechargeTime) = @TargetDate
        AND ri.RechargeMoney > 0
        AND ds.IsActive = 1 -- Only include active shops
    GROUP BY
        ri.RechargeShopId;

    -- 2. Update the Fact table using the temporary table.
    UPDATE fds
    SET
        fds.DailyRecharge_Total = rs.TotalRechargeAmount,
        fds.RechargeTier1_BatchCount = rs.RechargeTier1_BatchCount,
        fds.RechargeTier1_TotalAmount = rs.RechargeTier1_TotalAmount,
        fds.RechargeTier2_BatchCount = rs.RechargeTier2_BatchCount,
        fds.RechargeTier2_TotalAmount = rs.RechargeTier2_TotalAmount,
        fds.RechargeTier3_BatchCount = rs.RechargeTier3_BatchCount,
        fds.RechargeTier3_TotalAmount = rs.RechargeTier3_TotalAmount,
        fds.RechargeTier4_BatchCount = rs.RechargeTier4_BatchCount,
        fds.RechargeTier4_TotalAmount = rs.RechargeTier4_TotalAmount,
        fds.RechargeTier5_BatchCount = rs.RechargeTier5_BatchCount,
        fds.RechargeTier5_TotalAmount = rs.RechargeTier5_TotalAmount,
        fds.RechargeTier6_BatchCount = rs.RechargeTier6_BatchCount,
        fds.RechargeTier6_TotalAmount = rs.RechargeTier6_TotalAmount,
        fds.RechargeTier7_BatchCount = rs.RechargeTier7_BatchCount,
        fds.RechargeTier7_TotalAmount = rs.RechargeTier7_TotalAmount,
        fds.RechargeTier8_BatchCount = rs.RechargeTier8_BatchCount,
        fds.RechargeTier8_TotalAmount = rs.RechargeTier8_TotalAmount,
        fds.UpdateTime = GETDATE()
    FROM
        dbo.Fact_Daily_Shop_Summary AS fds
    JOIN
        dbo.Dim_Date AS dd ON fds.DateSK = dd.DateSK
    JOIN
        dbo.Dim_Shop AS ds ON fds.ShopSK = ds.ShopSK
    JOIN
        #RechargeSummary AS rs ON ds.ShopID = rs.ShopId
    WHERE
        dd.FullDate = @TargetDate;

    DROP TABLE #RechargeSummary;

    PRINT 'Successfully updated recharge data in Fact_Daily_Shop_Summary for ' + CONVERT(VARCHAR, @TargetDate, 120) + ' (Active Shops Only).';

END
GO