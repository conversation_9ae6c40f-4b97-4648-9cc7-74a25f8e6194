
-- ===================================================================
-- 脚本: fix_sync_open_data_procedure.sql
-- 功能: 修改 usp_Sync_RMS_DailyOpenData 存储过程，使用 TRY_CONVERT 修复日期转换错误
-- ===================================================================

USE operatedata;
GO

ALTER PROCEDURE usp_Sync_RMS_DailyOpenData
    @SyncDate DATE = NULL -- 添加一个可选的日期参数，默认为NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TargetBusinessDate DATE;

    -- 如果没有提供 @SyncDate 参数，则计算默认日期
    IF @SyncDate IS NULL
    BEGIN
        -- 假设默认同步前一天的业务数据，且业务日期转换逻辑不变
        SET @TargetBusinessDate = CAST(DATEADD(hour, -9, GETDATE() - 1) AS DATE);
    END
    ELSE
    BEGIN
        SET @TargetBusinessDate = @SyncDate;
    END

    -- 定义用于 ComeDate 筛选的日期范围
    DECLARE @StartDate DATETIME = CAST(@TargetBusinessDate AS DATETIME);
    DECLARE @EndDate DATETIME = DATEADD(day, 1, CAST(@TargetBusinessDate AS DATETIME));

    BEGIN TRY
        -- 同步 cloudRms2019.rms2019.dbo.opencacheinfo
        MERGE INTO operatedata.dbo.opencacheinfo AS T
        USING (
            SELECT * 
            FROM cloudRms2019.rms2019.dbo.opencacheinfo 
            -- 使用 TRY_CONVERT 避免因脏数据导致转换失败
            WHERE TRY_CONVERT(DATETIME, ComeDate) >= @StartDate AND TRY_CONVERT(DATETIME, ComeDate) < @EndDate
        ) AS S
        ON T.Ikey = S.Ikey
        WHEN MATCHED THEN UPDATE SET 
            T.CheckinStatus = S.CheckinStatus, 
            T.Invno = S.Invno, 
            T.RmNo = S.RmNo
        WHEN NOT MATCHED BY TARGET THEN INSERT 
            (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark) 
        VALUES
            (S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark);

        -- 同步 cloudRms2019.rms2019.dbo.openhistory
        MERGE INTO operatedata.dbo.opencacheinfo AS T
        USING (
            SELECT * 
            FROM cloudRms2019.rms2019.dbo.openhistory 
            -- 同样使用 TRY_CONVERT
            WHERE TRY_CONVERT(DATETIME, ComeDate) >= @StartDate AND TRY_CONVERT(DATETIME, ComeDate) < @EndDate
        ) AS S
        ON T.Ikey = S.Ikey
        WHEN NOT MATCHED BY TARGET THEN INSERT 
            (Ikey,BookNo,ShopId,CustKey,CustName,CustTel,ComeDate,ComeTime,Beg_Key,Beg_Name,End_Key,End_Name,Numbers,RtNo,RtName,CtNo,CtName,PtNo,PtName,BookMemory,BookStatus,CheckinStatus,BookShopId,BookUserId,BookUserName,BookDateTime,Invno,Openmemory,OrderUserID,OrderUserName,RmNo,Val1,FromRmNo,IsBirthday,Remark) 
        VALUES
            (S.Ikey,S.BookNo,S.ShopId,S.CustKey,S.CustName,S.CustTel,S.ComeDate,S.ComeTime,S.Beg_Key,S.Beg_Name,S.End_Key,S.End_Name,S.Numbers,S.RtNo,S.RtName,S.CtNo,S.CtName,S.PtNo,S.PtName,S.BookMemory,S.BookStatus,S.CheckinStatus,S.BookShopId,S.BookUserId,S.BookUserName,S.BookDateTime,S.Invno,S.Openmemory,S.OrderUserID,S.OrderUserName,S.RmNo,S.Val1,S.FromRmNo,S.IsBirthday,S.Remark);
            
    END TRY
    BEGIN CATCH
        -- 发生错误时，打印更详细的信息
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();

        PRINT 'Error occurred in usp_Sync_RMS_DailyOpenData:';
        PRINT 'Error Message: ' + @ErrorMessage;
        PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS VARCHAR(10));
        PRINT 'Error State: ' + CAST(@ErrorState AS VARCHAR(10));
        
        -- 重新抛出错误，以便调用方（如SQL Agent Job）能捕获到失败状态
        RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
