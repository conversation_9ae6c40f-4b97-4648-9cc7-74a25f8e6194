BEGIN TRANSACTION;

-- Step 1: Insert Bank Deals
INSERT INTO Dim_Deal_Item_Mapping (Source_FdName, Channel, SubChannel, IsFeeApplicable, FeeRate)
SELECT DISTINCT
    d.DealName,
    '银行',
    b.BankName,
    1, -- Assuming bank deals have fees, can be adjusted
    0.006 -- Assuming a default 0.6% fee for banks, can be adjusted
FROM 
    Dim_Bank_Deal d
JOIN 
    Dim_Bank b ON d.BankSK = b.BankSK
WHERE 
    NOT EXISTS (SELECT 1 FROM Dim_Deal_Item_Mapping m WHERE m.Source_FdName = d.DealName);

-- Step 2: Inser<PERSON> Meituan and <PERSON><PERSON><PERSON> Deals
INSERT INTO Dim_Deal_Item_Mapping (Source_FdName, Channel, SubChannel, IsFeeApplicable, FeeRate)
SELECT DISTINCT
    FdCName,
    CASE 
        WHEN FdCName LIKE '%美团%' OR FdCName LIKE '%美预%' THEN '美团'
        WHEN FdCName LIKE '%抖音%' OR FdCName LIKE '%抖预%' THEN '抖音'
    END,
    CASE 
        WHEN FdCName LIKE '%美预%' OR FdCName LIKE '%抖预%' THEN '预约'
        ELSE '团购'
    END,
    1, -- Assuming these deals have fees
    0.03 -- Assuming a default 3% fee, can be adjusted
FROM
    food
WHERE
    (FdCName LIKE '%美团%' OR FdCName LIKE '%美预%' OR FdCName LIKE '%抖音%' OR FdCName LIKE '%抖预%')
    AND NOT EXISTS (SELECT 1 FROM Dim_Deal_Item_Mapping m WHERE m.Source_FdName = food.FdCName);

COMMIT TRANSACTION;