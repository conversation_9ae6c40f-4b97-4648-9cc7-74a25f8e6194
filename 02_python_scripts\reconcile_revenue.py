
import pyodbc
import os

# Connection details from memory
server = '***********'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'

# Connection string
conn_str = (
    f'DRIVER={{ODBC Driver 17 for SQL Server}};'
    f'SERVER={server};'
    f'DATABASE={database};'
    f'UID={username};'
    f'PWD={password};'
    f'TrustServerCertificate=yes;'
)

# SQL Query
sql_query = """
WITH TotalRevenueCTE AS (
    SELECT
        SUM(Cash + Cash_Targ*0.8 + Vesa + GZ + AccOkZD + RechargeAccount + NoPayed + WXPay + AliPay + MTPay + DZPay + NMPay + [Check] + WechatDeposit + WechatShopping + ReturnAccount + ISNULL(WechatOfficialPay, 0)) AS TotalRevenue
    FROM
        operatedata.dbo.rmcloseinfo
    WHERE
        ShopId = 2
        AND CloseDatetime >= '2025-08-31 08:00:00' AND CloseDatetime < '2025-09-01 06:00:00'
),
BatchSizeRevenueCTE AS (
    SELECT
        SUM(T.BillAmount) AS TotalBatchSizeRevenue
    FROM (
        SELECT DISTINCT
            rci.InvNo,
            (rci.Cash + rci.Cash_Targ*0.8 + rci.Vesa + GZ + AccOkZD + RechargeAccount + NoPayed + WXPay + AliPay + MTPay + DZPay + NMPay + [Check] + WechatDeposit + WechatShopping + ReturnAccount + ISNULL(rci.WechatOfficialPay, 0)) AS BillAmount
        FROM
            operatedata.dbo.rmcloseinfo AS rci
        JOIN
            operatedata.dbo.fdcashbak AS fd ON rci.InvNo = fd.InvNo
        WHERE
            rci.WorkDate = '2025-08-31'
            AND rci.ShopId = 2
            AND fd.FdCName LIKE N'%消费人数%'
            AND fd.CashType = 'N'
    ) AS T
)
SELECT
    ISNULL(T_Rev.TotalRevenue, 0) AS Calculated_TotalRevenue,
    ISNULL(B_Rev.TotalBatchSizeRevenue, 0) AS Calculated_BatchSizeRevenue
FROM
    TotalRevenueCTE AS T_Rev,
    BatchSizeRevenueCTE AS B_Rev;
"""

try:
    print("Connecting to the database...")
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    print("Connection successful. Executing query...")
    
    cursor.execute(sql_query)
    
    row = cursor.fetchone()
    
    print("Query executed. Results:")
    print("----------------------------------------------------------")
    if row:
        print(f"Calculated_TotalRevenue: {row.Calculated_TotalRevenue}")
        print(f"Calculated_BatchSizeRevenue: {row.Calculated_BatchSizeRevenue}")
    else:
        print("No rows returned.")
    print("----------------------------------------------------------")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("Connection closed.")

