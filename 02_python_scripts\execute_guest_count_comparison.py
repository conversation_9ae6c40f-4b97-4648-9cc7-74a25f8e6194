import pyodbc

# Database connection details
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\compare_guest_count_methods_v2.sql'

cnxn = None
cursor = None
try:
    # Read the SQL query from the file
    # Specify utf-8-sig to handle potential BOM from some editors
    with open(sql_file_path, 'r', encoding='utf-8-sig') as f:
        sql_query = f.read()

    # Connect to the database
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    # Execute the query
    cursor.execute(sql_query)
    
    # Fetch the results
    rows = cursor.fetchall()
    columns = [column[0] for column in cursor.description]

    # Print header
    header = ' | '.join(f'{col:<25}' for col in columns)
    print(header)
    print('-' * len(header))

    # Print rows
    if rows:
        for row in rows:
            # Format each cell to align with the header
            formatted_row = ' | '.join(f'{str(item):<25}' for item in row)
            print(formatted_row)
    else:
        print("No data returned for the given date and active shops.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
