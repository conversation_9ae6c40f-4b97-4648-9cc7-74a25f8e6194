import pyodbc

# 数据库连接信息
server = '192.168.2.5'
database = 'operatedata'
username = ''  # 根据实际情况填写
password = ''  # 根据实际情况填写

# 连接字符串
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

try:
    # 建立连接
    conn = pyodbc.connect(conn_str)
    cursor = conn.cursor()
    
    # 查询所有表名，查找与储值相关的表
    cursor.execute("""
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND (TABLE_NAME LIKE '%储值%' OR TABLE_NAME LIKE '%stored%' OR TABLE_NAME LIKE '%value%')
    """)
    
    tables = cursor.fetchall()
    
    if tables:
        print("找到与储值相关的表:")
        for table in tables:
            print(f"- {table[0]}")
    else:
        print("未找到与储值直接相关的表")
        
    # 查询FdCashBak表结构，看是否有储值相关字段
    cursor.execute("""
        SELECT COLUMN_NAME, DATA_TYPE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'FdCashBak' 
        AND (COLUMN_NAME LIKE '%储值%' OR COLUMN_NAME LIKE '%stored%' OR COLUMN_NAME LIKE '%value%')
    """)
    
    columns = cursor.fetchall()
    
    if columns:
        print("\nFdCashBak表中与储值相关的字段:")
        for column in columns:
            print(f"- {column[0]} ({column[1]})")
    else:
        print("\nFdCashBak表中未找到与储值直接相关的字段")
        
    # 关闭连接
    conn.close()
    
except Exception as e:
    print(f"连接数据库时出错: {e}")