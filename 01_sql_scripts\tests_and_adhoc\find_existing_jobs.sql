SELECT
    j.name AS JobName,
    j.description AS JobDescription,
    s.step_id AS StepId,
    s.step_name AS StepName,
    s.command AS Command,
    sched.name AS ScheduleName,
    CASE sched.freq_type
        WHEN 1 THEN 'One time'
        WHEN 4 THEN 'Daily'
        WHEN 8 THEN 'Weekly'
        WHEN 16 THEN 'Monthly'
        WHEN 32 THEN 'Monthly relative'
        WHEN 64 THEN 'When SQL Server Agent starts'
        WHEN 128 THEN 'When the computer is idle'
        ELSE 'Unknown'
    END AS Frequency,
    STUFF(STUFF(RIGHT('000000' + CAST(sched.active_start_time AS VARCHAR(6)), 6), 3, 0, ':'), 6, 0, ':') AS StartTime
FROM
    dbo.sysjobs j
JOIN
    dbo.sysjobsteps s ON j.job_id = s.job_id
LEFT JOIN
    dbo.sysjobschedules js ON j.job_id = js.job_id
LEFT JOIN
    dbo.sysschedules sched ON js.schedule_id = sched.schedule_id
WHERE
    j.enabled = 1
ORDER BY
    j.name, s.step_id;