import pyodbc
import os
import cgi

# Define the output file path
output_file_path = os.path.join('03_reports', 'mapping_verification.html')

# Connection details
conn_str = """
    DRIVER={ODBC Driver 17 for SQL Server};
    SERVER=192.168.2.5;
    DATABASE=operatedata;
    UID=sa;
    PWD=Musicbox123;
    TrustServerCertificate=yes;
"""

# SQL query
sql_query = "SELECT Source_FdName, Channel, SubChannel, FeeRate FROM Dim_Deal_Item_Mapping ORDER BY Channel, SubChannel, Source_FdName;"

# HTML template with placeholders
html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>映射表审核</title>
    <style>{css_styles}</style>
</head>
<body>
    <h2>Dim_Deal_Item_Mapping 内容审核</h2>
    <table>
        <tr>
            <th>Source_FdName</th>
            <th>Channel</th>
            <th>SubChannel</th>
            <th>FeeRate</th>
        </tr>
        {table_rows}
    </table>
</body>
</html>
"""

# CSS styles as a separate string
css_styles = """
    table { width: 80%; border-collapse: collapse; margin: 20px auto; font-family: \"Microsoft YaHei\", sans-serif; }
    th, td { border: 1px solid #dddddd; text-align: left; padding: 8px; }
    th { background-color: #f2f2f2; }
"""

try:
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    cursor.execute(sql_query)
    rows = cursor.fetchall()
    
    table_rows_html = ""
    if not rows:
        table_rows_html = "<tr><td colspan='4'>映射表中没有数据。</td></tr>"
    else:
        for row in rows:
            # Escape HTML special characters in data
            source_fdname = cgi.escape(str(row.Source_FdName))
            channel = cgi.escape(str(row.Channel))
            subchannel = cgi.escape(str(row.SubChannel))
            feerate = cgi.escape(str(row.FeeRate))
            table_rows_html += f"<tr><td>{source_fdname}</td><td>{channel}</td><td>{subchannel}</td><td>{feerate}</td></tr>\n"
            
    # Format the template with styles and data
    final_html = html_template.format(css_styles=css_styles, table_rows=table_rows_html)
    
    with open(output_file_path, 'w', encoding='utf-8') as f:
        f.write(final_html)
        
    print(f"验证文件已生成: {output_file_path}")

except Exception as e:
    print(f"操作出错: {e}")
finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
