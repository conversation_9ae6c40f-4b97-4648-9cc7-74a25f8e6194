
import csv
import re
import os

# --- Configuration ---
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report.txt')
output_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report_cleaned.csv')

# Regex to find potential song numbers (alphanumeric, at least 6 chars, may start with a quote)
# It will match things like '00612689, 40019907, A002A94, 9953B72, 00004c76
SONG_ID_REGEX = r"['a-zA-Z0-9]{6,}"


def find_representative_song_id(row_data):
    # Combine the relevant columns into a single string for searching
    # Columns are: 备注 (6), 相同 (7), 不相同 (8)
    search_string = ' '.join(row_data[6:9])
    
    # Find all potential matches
    matches = re.findall(SONG_ID_REGEX, search_string)
    
    # Return the first match if any are found, otherwise return an empty string
    return matches[0] if matches else ''

# --- Main Execution ---

# Ensure the output directory exists
os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

cleaned_rows = []

# Read the source file
with open(input_file_path, 'r', encoding='utf-8') as f_in:
    # Skip the first line which is a sheet name header
    next(f_in)
    reader = csv.reader(f_in)
    
    # Read header row
    try:
        header = next(reader)
        header.append('统一歌号')
        cleaned_rows.append(header)

        # Process each data row
        for row in reader:
            # Skip empty rows or other sheet headers
            if not row or row[0].startswith('--- Sheet:'):
                continue
            
            # Ensure row has enough columns to avoid index errors
            if len(row) > 8:
                song_id = find_representative_song_id(row)
                row.append(song_id)
            else:
                # If not enough columns, just append an empty string for the new column
                row.append('')
            
            cleaned_rows.append(row)
            
    except StopIteration:
        print("文件为空或格式不正确。")

# Write the cleaned data to a new CSV file
if cleaned_rows:
    with open(output_file_path, 'w', newline='', encoding='utf-8-sig') as f_out:
        writer = csv.writer(f_out)
        writer.writerows(cleaned_rows)
    print(f"清理完成，已将结果保存到: {os.path.abspath(output_file_path)}")
else:
    print("没有数据被处理。")

