import pyodbc

# Database connection details
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\temp_update_sp_batchsize_v2.sql'

cnxn = None
cursor = None
try:
    with open(sql_file_path, 'r', encoding='utf-8-sig') as f:
        sql_query = f.read()

    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    cursor.execute(sql_query)
    cnxn.commit()
    print("Successfully executed the script to update usp_Populate_Fact_Daily_BatchSize_Summary.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
