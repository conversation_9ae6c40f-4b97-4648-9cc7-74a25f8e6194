
import pyodbc

# --- Configuration ---
DB_SERVER = '192.168.2.5'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
TABLES_TO_INSPECT = [
    'DynamicReport_Header',
    'DynamicReport_TimeSlotDetails',
    'FullDailyReport_Header',
    'FullDailyReport_NightDetails',
    'FullDailyReport_TimeSlotDetails',
    'Fact_Deal_Redemption'
]

def get_detailed_schema_with_comments():
    """Connects to the database and retrieves schema and comments for specified tables."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
    
    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功。\n")
        
        for table_name in TABLES_TO_INSPECT:
            print(f"--- 表名: {table_name} ---")
            
            sql = """
            SELECT 
                c.name AS [ColumnName],
                t.name AS [DataType],
                c.max_length AS [MaxLength],
                c.is_nullable AS [IsNullable],
                CAST(ep.value AS NVARCHAR(MAX)) AS [Description]
            FROM 
                sys.columns c
            INNER JOIN 
                sys.tables st ON st.object_id = c.object_id
            INNER JOIN
                sys.types t ON t.user_type_id = c.user_type_id
            LEFT JOIN 
                sys.extended_properties ep ON ep.major_id = c.object_id AND ep.minor_id = c.column_id AND ep.name = 'MS_Description'
            WHERE 
                st.name = ?
            ORDER BY 
                c.column_id;
            """
            
            try:
                cursor.execute(sql, table_name)
                rows = cursor.fetchall()
                
                if not rows:
                    print(f"无法找到表 '{table_name}' 或该表没有列。")
                else:
                    header = f"{'字段名':<30} {'数据类型':<15} {'是否可空':<10} {'中文注释'}"
                    print(header)
                    print("-" * 80)
                    for row in rows:
                        nullable_str = 'YES' if row.IsNullable else 'NO'
                        description = row.Description if row.Description else ''
                        print(f"{row.ColumnName:<30} {row.DataType:<15} {nullable_str:<10} {description}")
                print("\n")

            except pyodbc.Error as ex:
                print(f"查询表 '{table_name}' 时出错: {ex}")
                print("\n")

    except pyodbc.Error as ex:
        print(f"数据库连接或操作出错: {ex}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    get_detailed_schema_with_comments()
