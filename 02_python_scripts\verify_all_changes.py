import pyodbc

# --- Configuration ---
db_connection_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
target_date = '2025-09-03'
target_shop_id = 3 # Tianhe Shop, since we know it has interesting data

# --- SQL Queries ---
# Using stored procedures requires a different execution method
sql_run_shop_summary = f"EXEC dbo.usp_Populate_Fact_Daily_Shop_Summary @TargetDate = '{target_date}', @ShopId = {target_shop_id}"
sql_run_batch_summary = f"EXEC dbo.usp_Populate_Fact_Daily_BatchSize_Summary @RunDate = '{target_date}'"

# We need the DateSK and ShopSK for querying the fact tables
sql_get_sks = f"SELECT DateSK FROM dbo.Dim_Date WHERE FullDate = '{target_date}'; SELECT ShopSK FROM dbo.Dim_Shop WHERE ShopID = {target_shop_id}"

sql_query_shop_summary = """
SELECT
    DateSK, ShopSK,
    BuffetGuestCount, NightTimeGuestCount, TotalDirectFallGuests, -- Check guest counts
    ComplimentaryBatches, ComplimentaryRevenue, -- Check complimentary data
    DailyRecharge_Meituan, DailyRecharge_Meituan_Ratio -- Check Meituan recharge
FROM dbo.Fact_Daily_Shop_Summary
WHERE DateSK = ? AND ShopSK = ?
"""

sql_query_batch_summary = """
SELECT
    Category, TotalBatches, TotalAmount,
    Batches_2_Person, Batches_3_Person, Batches_4_Person, Batches_5_Person,
    Batches_6_Person, Batches_7_Person, Batches_8_Person, Batches_9_Person,
    Batches_10_Plus_Person
FROM dbo.Fact_Daily_BatchSize_Summary
WHERE DateSK = ? AND ShopSK = ?
ORDER BY Category
"""

# --- Execution ---
cnxn = None
cursor = None
try:
    cnxn = pyodbc.connect(db_connection_str)
    cursor = cnxn.cursor()

    print(f"--- Running ETL for ShopID={target_shop_id}, Date='{target_date}' ---")
    print("Running usp_Populate_Fact_Daily_Shop_Summary...")
    cursor.execute(sql_run_shop_summary)
    cnxn.commit()
    print("Running usp_Populate_Fact_Daily_BatchSize_Summary...")
    cursor.execute(sql_run_batch_summary)
    cnxn.commit()
    print("ETL runs completed.")

    print("\n--- Fetching SKs for querying ---")
    cursor.execute(sql_get_sks)
    date_sk = cursor.fetchone()[0]
    cursor.nextset()
    shop_sk = cursor.fetchone()[0]
    print(f"DateSK = {date_sk}, ShopSK = {shop_sk}")

    print("\n--- Verifying Fact_Daily_Shop_Summary ---")
    cursor.execute(sql_query_shop_summary, date_sk, shop_sk)
    rows = cursor.fetchall()
    columns = [column[0] for column in cursor.description]
    print(' | '.join(columns))
    print('-' * 120)
    for row in rows:
        print(' | '.join(str(x) for x in row))

    print("\n--- Verifying Fact_Daily_BatchSize_Summary ---")
    cursor.execute(sql_query_batch_summary, date_sk, shop_sk)
    rows = cursor.fetchall()
    columns = [column[0] for column in cursor.description]
    print(' | '.join(columns))
    print('-' * 150)
    for row in rows:
        print(' | '.join(str(x) for x in row))

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
