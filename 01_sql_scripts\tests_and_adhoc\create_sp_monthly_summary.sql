ALTER PROCEDURE dbo.usp_Populate_Fact_Monthly_Summary
    @TargetYear INT,
    @TargetMonth INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Perform the DELETE operation first.
    DELETE FROM dbo.Fact_Monthly_Shop_Summary WHERE Year = @TargetYear AND Month = @TargetMonth;

    -- Define date ranges
    DECLARE @CurrentMonthStart DATE = DATEFROMPARTS(@TargetYear, @TargetMonth, 1);
    DECLARE @CurrentMonthEnd DATE = EOMONTH(@CurrentMonthStart);
    DECLARE @PreviousMonthStart DATE = DATEADD(MONTH, -1, @CurrentMonthStart);
    DECLARE @PreviousMonthEnd DATE = EOMONTH(@PreviousMonthStart);

    -- Now, define the CTEs and use them in the immediately following INSERT statement.
    WITH CurrentMonthData AS (
        SELECT
            s.ShopID,
            SUM(f.TotalRevenue) AS Revenue_Total,
            SUM(f.DayTimeRevenue + f.NightTimeRevenue) AS Revenue_Offline,
            SUM(f.Revenue_PrivilegeBooking) AS Revenue_PrivilegeBooking,
            SUM(f.Revenue_OfficialAccount) AS Revenue_OfficialAccount,
            SUM(f.Revenue_Meituan) AS Revenue_Meituan,
            SUM(f.Revenue_Douyin) AS Revenue_Douyin,
            SUM(f.Revenue_Bank) AS Revenue_Bank,
            SUM(f.PrivilegeBooking_Count_0Yuan) AS Count_PrivilegeBooking_0Yuan,
            SUM(f.PrivilegeBooking_Count_5Yuan) AS Count_PrivilegeBooking_5Yuan,
            SUM(f.PrivilegeBooking_Count_10Yuan) AS Count_PrivilegeBooking_10Yuan,
            SUM(f.PrivilegeBooking_Count_15Yuan) AS Count_PrivilegeBooking_15Yuan,
            SUM(f.Revenue_Douyin_Booking) AS Revenue_Douyin_Booking,
            SUM(f.Revenue_Douyin_GroupBuy) AS Revenue_Douyin_GroupBuy,
            SUM(f.Revenue_Meituan_Booking) AS Revenue_Meituan_Booking,
            SUM(f.Revenue_Meituan_GroupBuy) AS Revenue_Meituan_GroupBuy,
            SUM(f.Revenue_Bank_GF) AS Revenue_Bank_GF,
            SUM(f.Revenue_Bank_CITIC) AS Revenue_Bank_CITIC,
            SUM(f.Revenue_Bank_UnionPay) AS Revenue_Bank_UnionPay,
            SUM(f.Fee_Douyin_Booking) AS Fee_Douyin_Booking,
            SUM(f.Fee_Douyin_GroupBuy) AS Fee_Douyin_GroupBuy,
            SUM(f.Fee_Meituan_Booking) AS Fee_Meituan_Booking,
            SUM(f.Fee_Meituan_GroupBuy) AS Fee_Meituan_GroupBuy,
            SUM(f.Fee_Bank_Total) AS Fee_Bank_Total
        FROM dbo.Fact_Daily_Shop_Summary f
        JOIN dbo.Dim_Date d ON f.DateSK = d.DateSK
        JOIN dbo.Dim_Shop s ON f.ShopSK = s.ShopSK
        WHERE d.FullDate BETWEEN @CurrentMonthStart AND @CurrentMonthEnd
        GROUP BY s.ShopID
    ),
    PreviousMonthData AS (
        SELECT
            s.ShopID,
            SUM(f.TotalRevenue) AS Revenue_Total_Prev,
            SUM(f.DayTimeRevenue + f.NightTimeRevenue) AS Revenue_Offline_Prev,
            SUM(f.Revenue_PrivilegeBooking) AS Revenue_PrivilegeBooking_Prev,
            SUM(f.Revenue_OfficialAccount) AS Revenue_OfficialAccount_Prev,
            SUM(f.Revenue_Meituan) AS Revenue_Meituan_Prev,
            SUM(f.Revenue_Douyin) AS Revenue_Douyin_Prev,
            SUM(f.Revenue_Bank) AS Revenue_Bank_Prev
        FROM dbo.Fact_Daily_Shop_Summary f
        JOIN dbo.Dim_Date d ON f.DateSK = d.DateSK
        JOIN dbo.Dim_Shop s ON f.ShopSK = s.ShopSK
        WHERE d.FullDate BETWEEN @PreviousMonthStart AND @PreviousMonthEnd
        GROUP BY s.ShopID
    )
    INSERT INTO dbo.Fact_Monthly_Shop_Summary (
        Year, Month, ShopID,
        Revenue_Total, Revenue_Total_YoY, Revenue_Total_MoM,
        Revenue_Offline, Revenue_Offline_YoY, Revenue_Offline_MoM,
        Revenue_PrivilegeBooking, Revenue_PrivilegeBooking_YoY, Revenue_PrivilegeBooking_MoM,
        Revenue_OfficialAccount, Revenue_OfficialAccount_YoY, Revenue_OfficialAccount_MoM,
        Revenue_Meituan, Revenue_Meituan_YoY, Revenue_Meituan_MoM,
        Revenue_Douyin, Revenue_Douyin_YoY, Revenue_Douyin_MoM,
        Revenue_Bank, Revenue_Bank_YoY, Revenue_Bank_MoM,
        Revenue_OtherBusiness, Revenue_OtherBusiness_YoY, Revenue_OtherBusiness_MoM,
        Count_PrivilegeBooking_0Yuan, Count_PrivilegeBooking_5Yuan, Count_PrivilegeBooking_10Yuan, Count_PrivilegeBooking_15Yuan,
        Revenue_Douyin_Booking, Revenue_Douyin_GroupBuy, Revenue_Meituan_Booking, Revenue_Meituan_GroupBuy,
        Revenue_Bank_GF, Revenue_Bank_CITIC, Revenue_Bank_UnionPay,
        Fee_Douyin_Booking, Fee_Douyin_GroupBuy, Fee_Meituan_Booking, Fee_Meituan_GroupBuy, Fee_Bank_Total
    )
    SELECT
        @TargetYear,
        @TargetMonth,
        cmd.ShopID,
        cmd.Revenue_Total,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_Total_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_Total - pmd.Revenue_Total_Prev) / pmd.Revenue_Total_Prev END,
        cmd.Revenue_Offline,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_Offline_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_Offline - pmd.Revenue_Offline_Prev) / pmd.Revenue_Offline_Prev END,
        cmd.Revenue_PrivilegeBooking,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_PrivilegeBooking_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_PrivilegeBooking - pmd.Revenue_PrivilegeBooking_Prev) / pmd.Revenue_PrivilegeBooking_Prev END,
        cmd.Revenue_OfficialAccount,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_OfficialAccount_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_OfficialAccount - pmd.Revenue_OfficialAccount_Prev) / pmd.Revenue_OfficialAccount_Prev END,
        cmd.Revenue_Meituan,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_Meituan_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_Meituan - pmd.Revenue_Meituan_Prev) / pmd.Revenue_Meituan_Prev END,
        cmd.Revenue_Douyin,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_Douyin_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_Douyin - pmd.Revenue_Douyin_Prev) / pmd.Revenue_Douyin_Prev END,
        cmd.Revenue_Bank,
        0, -- YoY Placeholder
        CASE WHEN ISNULL(pmd.Revenue_Bank_Prev, 0) = 0 THEN 0 ELSE (cmd.Revenue_Bank - pmd.Revenue_Bank_Prev) / pmd.Revenue_Bank_Prev END,
        0, 0, 0, -- Placeholders for OtherBusiness
        cmd.Count_PrivilegeBooking_0Yuan, cmd.Count_PrivilegeBooking_5Yuan, cmd.Count_PrivilegeBooking_10Yuan, cmd.Count_PrivilegeBooking_15Yuan,
        cmd.Revenue_Douyin_Booking, cmd.Revenue_Douyin_GroupBuy, cmd.Revenue_Meituan_Booking, cmd.Revenue_Meituan_GroupBuy,
        cmd.Revenue_Bank_GF, cmd.Revenue_Bank_CITIC, cmd.Revenue_Bank_UnionPay,
        cmd.Fee_Douyin_Booking, cmd.Fee_Douyin_GroupBuy, cmd.Fee_Meituan_Booking, cmd.Fee_Meituan_GroupBuy, cmd.Fee_Bank_Total
    FROM CurrentMonthData cmd
    LEFT JOIN PreviousMonthData pmd ON cmd.ShopID = pmd.ShopID;

END