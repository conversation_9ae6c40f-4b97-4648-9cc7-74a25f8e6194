-- Find one invoice from Tianhe Shop (ShopId=3) on the target date with a discrepancy.
DECLARE @TargetDate DATE = '2025-09-03';
DECLARE @WorkDate_varchar VARCHAR(8) = CONVERT(varchar(8), @TargetDate, 112);
DECLARE @ShopId INT = 3; -- Tianhe Shop

WITH BillFuzzyCount AS (
    SELECT
        InvNo,
        SUM(ISNULL(FdQty, 0)) as FuzzyGuestCount
    FROM operatedata.dbo.fdcashbak
    WHERE
        FdCName LIKE N'%消费人数%'
        AND CashType = 'N'
    GROUP BY InvNo
)
SELECT TOP 1
    rci.InvNo
FROM operatedata.dbo.rmcloseinfo rci
JOIN BillFuzzyCount bfc ON rci.InvNo = bfc.InvNo
WHERE
    rci.WorkDate = @WorkDate_varchar
    AND rci.Shopid = @ShopId
    AND rci.Numbers <> bfc.FuzzyGuestCount
ORDER BY
    (bfc.FuzzyGuestCount - rci.Numbers) DESC; -- Find a bill where the fuzzy count is much larger
