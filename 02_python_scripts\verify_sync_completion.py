# This script verifies the data synchronization by comparing record counts.

import pyodbc

# Connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Query for source tables: Count of unique Ikeys
SOURCE_SQL = """
SELECT COUNT(DISTINCT T.Ikey) AS TotalCount
FROM (
    SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory
    UNION ALL
    SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory_bak
) AS T;
"""

# Query for destination table
DEST_SQL = "SELECT COUNT(Ikey) AS TotalCount FROM operatedata.dbo.opencacheinfo;"

def main():
    print("Connecting to the database...")
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        print("Executing queries to verify synchronization counts...")

        # Get source count
        print("  - Counting unique records from source (openhistory + openhistory_bak)...")
        cursor.execute(SOURCE_SQL)
        source_count = cursor.fetchone().TotalCount
        print(f"    Source Count: {source_count:,}")

        # Get destination count
        print("  - Counting records from destination (opencacheinfo)...")
        cursor.execute(DEST_SQL)
        dest_count = cursor.fetchone().TotalCount
        print(f"    Destination Count: {dest_count:,}")

        # Compare and report
        print("-"*50)
        difference = dest_count - source_count
        
        print(f"Source Unique Records: {source_count:,}")
        print(f"Destination Total Records: {dest_count:,}")
        print(f"Difference (Destination - Source): {difference:,}")

        if abs(difference) < 5000: # Allowing a small margin
            print("\nResult: Verification successful. The record counts are very close.")
        else:
            print("\nResult: Warning! A significant difference in record counts was found.")

    except pyodbc.Error as ex:
        print(f"Database query failed: {ex}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("\nConnection closed.")

if __name__ == "__main__":
    main()
