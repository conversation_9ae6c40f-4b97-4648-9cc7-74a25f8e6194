# This script performs a two-step verification to check for extra records.

import pyodbc

# Connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Step 1: Find a sample of 10 keys that are in the destination but not in the source.
STEP_1_SQL = """
SELECT TOP 10 oci.Ikey
FROM operatedata.dbo.opencacheinfo oci
WHERE NOT EXISTS (
    SELECT 1
    FROM (
        SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory
        UNION ALL
        SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory_bak
    ) AS SourceData
    WHERE SourceData.Ikey = oci.Ikey
);
"""

def main():
    print("Connecting to the database...")
    sample_keys = []
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        # --- Step 1: Get Sample Keys ---
        print("Step 1: Finding a sample of 10 'extra' records from destination...")
        cursor.execute(STEP_1_SQL)
        rows = cursor.fetchall()
        if not rows:
            print("Could not find any 'extra' records to test. This is unexpected.")
            return

        sample_keys = [row.Ikey for row in rows]
        print(f"Found {len(sample_keys)} sample keys: {sample_keys}")

        # --- Step 2: Check if these keys exist in the remote openhistory table ---
        print("\nStep 2: Checking if these specific keys exist in remote 'openhistory' table...")
        
        # Prepare a parameterized query
        placeholders = ', '.join('?' * len(sample_keys))
        STEP_2_SQL = f"SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory WHERE Ikey IN ({placeholders});"
        
        cursor.execute(STEP_2_SQL, sample_keys)
        found_keys = cursor.fetchall()
        
        print("-"*70)
        if not found_keys:
            print("Result: As expected, none of the 10 sample 'extra' keys were found in the remote openhistory table.")
        else:
            print(f"Result: Unexpectedly, found {len(found_keys)} matching keys: {[row.Ikey for row in found_keys]}")
        print("This confirms that the extra records in the destination are not present in the source tables.")
        print("-"*70)

    except pyodbc.Error as ex:
        print(f"Database query failed: {ex}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("\nConnection closed.")

if __name__ == "__main__":
    main()
