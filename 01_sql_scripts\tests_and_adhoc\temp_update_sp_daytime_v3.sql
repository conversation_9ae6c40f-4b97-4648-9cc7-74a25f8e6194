ALTER PROCEDURE dbo.usp_Calculate_DayTimeMetrics_ForStaging
    @ShopId INT,
    @TargetDate DATE,
    @ShopSK INT,
    @DateSK INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @BeginDate DATETIME = DATEADD(hour, 8, CAST(@TargetDate AS DATETIME));
    DECLARE @EndDate DATETIME = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS DATETIME));

    -- NEW CTE: Calculate guest count per bill using the reliable fuzzy sum method
    WITH FuzzyGuestCountsPerBill AS (
        SELECT
            InvNo,
            ShopId,
            SUM(ISNULL(FdQty, 0)) as GuestCount
        FROM operatedata.dbo.fdcashbak
        WHERE
            FdCName LIKE N'%消费人数%'
            AND CashType = 'N'
        GROUP BY InvNo, ShopId
    ),
    BaseRecords AS (
        SELECT
            rt.InvNo, rt.<PERSON><PERSON>, rt.Close<PERSON>ate<PERSON>, rt.<PERSON>t<PERSON><PERSON>, rt.<PERSON>, rt.<PERSON><PERSON><PERSON>, rt.DZPay, rt.AccOkZD,
            (rt.Cash+rt.Cash_Targ*0.8+rt.Vesa+rt.GZ+rt.AccOkZD+rt.RechargeAccount+rt.NoPayed+rt.WXPay+rt.AliPay+rt.MTPay+rt.DZPay+rt.NMPay+rt.[Check]+rt.WechatDeposit+rt.WechatShopping+rt.ReturnAccount) AS TotalRevenue,
            rt.WechatOfficialPay,
            -- MODIFICATION: Replace rt.Numbers with our new fuzzy count. All downstream calculations will now use this.
            ISNULL(fgc.GuestCount, 0) AS Numbers,
            rt.IsDirectFall,
            ISNULL(sti.TimeMode, CASE WHEN DATEPART(hour, rt.CloseDatetime) < 20 THEN 1 ELSE 2 END) AS TimeMode
        FROM dbo.RmCloseInfo rt
        -- Join with the new CTE to get the fuzzy guest count
        LEFT JOIN FuzzyGuestCountsPerBill fgc ON rt.InvNo = fgc.InvNo AND rt.Shopid = fgc.ShopId
        LEFT JOIN dbo.shoptimeinfo sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
        WHERE rt.Shopid = @ShopId AND rt.CloseDatetime BETWEEN @BeginDate AND @EndDate
    ),
    ChannelMetrics AS (
        SELECT
            b.InvNo,
            SUM(CASE WHEN dc.ChannelSK = 3 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Meituan_Booking,
            SUM(CASE WHEN dc.ChannelSK = 4 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Meituan_GroupBuy,
            SUM(CASE WHEN dc.ChannelSK = 5 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Douyin_Booking,
            SUM(CASE WHEN dc.ChannelSK = 1 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Douyin_GroupBuy,
            SUM(CASE WHEN dc.ChannelSK = 6 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Bank_GF,
            SUM(CASE WHEN dc.ChannelSK = 7 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Bank_CITIC,
            SUM(CASE WHEN dc.ChannelSK = 2 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty ELSE 0 END) AS Revenue_Bank_UnionPay,
            SUM(CASE WHEN dc.ChannelSK = 3 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Meituan_Booking,
            SUM(CASE WHEN dc.ChannelSK = 4 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Meituan_GroupBuy,
            SUM(CASE WHEN dc.ChannelSK = 5 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Douyin_Booking,
            SUM(CASE WHEN dc.ChannelSK = 1 THEN ISNULL(ddm.FdPrice2, 0) * b.FdQty * ISNULL(dc.FeeRate, 0) ELSE 0 END) AS Fee_Douyin_GroupBuy,
            SUM(CASE WHEN dc.ChannelSK = 6 THEN ISNULL(dbd.ServiceFee, 0) ELSE 0 END) AS Fee_Bank_GF,
            SUM(CASE WHEN dc.ChannelSK = 7 THEN ISNULL(dbd.ServiceFee, 0) ELSE 0 END) AS Fee_Bank_CITIC,
            SUM(CASE WHEN dc.ChannelSK = 2 THEN ISNULL(dbd.ServiceFee, 0) ELSE 0 END) AS Fee_Bank_UnionPay
        FROM dbo.FdCashBak AS b
        INNER JOIN dbo.Dim_Deal_Map AS ddm ON b.ShopId = ddm.ShopId AND b.FdNo = ddm.FdNo COLLATE Chinese_PRC_Stroke_CI_AS
        INNER JOIN dbo.Dim_Channel AS dc ON ddm.ChannelSK = dc.ChannelSK
        LEFT JOIN dbo.Dim_Bank_Deal AS dbd ON b.FdNo = dbd.FdNo COLLATE Chinese_PRC_Stroke_CI_AS
        WHERE b.Shopid = @ShopId AND EXISTS (SELECT 1 FROM BaseRecords br WHERE br.InvNo = b.InvNo)
        GROUP BY b.InvNo
    ),
    CategorizedNightInvoices AS (
        SELECT DISTINCT r.InvNo, fdc.FdCName
        FROM BaseRecords r
        JOIN dbo.FdCashBak fdc ON r.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
        WHERE r.TimeMode = 2 AND fdc.ShopId = @ShopId
    ),
    FinalMetrics AS (
        SELECT
            SUM(ISNULL(br.TotalRevenue, 0)) AS TotalRevenue,
            SUM(CASE WHEN br.TimeMode = 1 THEN br.TotalRevenue ELSE 0 END) AS DayTimeRevenue,
            SUM(CASE WHEN br.TimeMode = 2 THEN br.TotalRevenue ELSE 0 END) AS NightTimeRevenue,
            SUM(CASE WHEN br.TimeMode = 1 THEN br.Numbers ELSE 0 END) AS BuffetGuestCount, -- This now uses the fuzzy count
            SUM(CASE WHEN br.IsDirectFall = 1 THEN br.Numbers ELSE 0 END) AS TotalDirectFallGuests, -- This now uses the fuzzy count
            COUNT(DISTINCT br.InvNo) AS TotalBatches,
            COUNT(DISTINCT CASE WHEN br.TimeMode = 1 THEN br.InvNo END) AS DayTimeBatchCount,
            COUNT(DISTINCT CASE WHEN br.TimeMode = 2 THEN br.InvNo END) AS NightTimeBatchCount,
            SUM(CASE WHEN br.TimeMode = 2 THEN br.Numbers ELSE 0 END) AS NightTimeGuestCount, -- This now uses the fuzzy count
            SUM(ISNULL(br.WechatOfficialPay, 0)) AS Revenue_OfficialAccount,
            COUNT(DISTINCT CASE WHEN ISNULL(br.AccOkZD, 0) > 0 THEN br.InvNo END) AS ComplimentaryBatches,
            SUM(CASE WHEN ISNULL(br.AccOkZD, 0) > 0 THEN br.AccOkZD ELSE 0 END) AS ComplimentaryRevenue,
            COUNT(DISTINCT CASE WHEN br.AliPay = 0 THEN br.InvNo END) AS PrivilegeBooking_Count_0Yuan,
            COUNT(DISTINCT CASE WHEN br.AliPay = 5 THEN br.InvNo END) AS PrivilegeBooking_Count_5Yuan,
            COUNT(DISTINCT CASE WHEN br.AliPay = 10 THEN br.InvNo END) AS PrivilegeBooking_Count_10Yuan,
            COUNT(DISTINCT CASE WHEN br.AliPay = 15 THEN br.InvNo END) AS PrivilegeBooking_Count_15Yuan,
            ISNULL(SUM(CASE WHEN br.AliPay IN (0, 5, 10, 15) THEN br.AliPay ELSE 0 END), 0) AS Revenue_PrivilegeBooking,
            COUNT(DISTINCT CASE WHEN br.TimeMode = 2 AND br.CtNo = 19 THEN br.InvNo END) AS Night_FreeMeal_Batches,
            ISNULL(SUM(CASE WHEN br.TimeMode = 2 AND br.CtNo = 19 THEN br.TotalRevenue ELSE 0 END), 0) AS Night_FreeMeal_Revenue,
            (SELECT COUNT(DISTINCT InvNo) FROM CategorizedNightInvoices WHERE FdCName LIKE N'%买断%') AS Night_Buyout_Batches,
            (SELECT COUNT(DISTINCT InvNo) FROM CategorizedNightInvoices WHERE FdCName LIKE N'%畅饮%' AND FdCName NOT LIKE N'%自由畅饮%') AS Night_DrinkPackage_Batches,
            (SELECT COUNT(DISTINCT InvNo) FROM CategorizedNightInvoices WHERE FdCName LIKE N'%自由畅饮%') AS Night_FreeConsumption_Batches,
            (SELECT COUNT(DISTINCT InvNo) FROM CategorizedNightInvoices WHERE FdCName LIKE N'%年卡%') AS Night_Other_Batches,
            (COUNT(DISTINCT CASE WHEN br.TimeMode = 2 THEN br.InvNo END) - COUNT(DISTINCT CASE WHEN br.TimeMode = 2 AND br.CtNo = 19 THEN br.InvNo END) - (SELECT COUNT(DISTINCT InvNo) FROM CategorizedNightInvoices WHERE FdCName LIKE N'%买断%' OR FdCName LIKE N'%畅饮%' OR FdCName LIKE N'%年卡%')) AS Night_RoomFee_Batches,
            SUM(ISNULL(cm.Revenue_Meituan_Booking, 0)) AS Revenue_Meituan_Booking, SUM(ISNULL(cm.Revenue_Meituan_GroupBuy, 0)) AS Revenue_Meituan_GroupBuy,
            SUM(ISNULL(cm.Revenue_Douyin_Booking, 0)) AS Revenue_Douyin_Booking, SUM(ISNULL(cm.Revenue_Douyin_GroupBuy, 0)) AS Revenue_Douyin_GroupBuy,
            SUM(ISNULL(cm.Revenue_Bank_GF, 0)) AS Revenue_Bank_GF, SUM(ISNULL(cm.Revenue_Bank_CITIC, 0)) AS Revenue_Bank_CITIC, SUM(ISNULL(cm.Revenue_Bank_UnionPay, 0)) AS Revenue_Bank_UnionPay,
            SUM(ISNULL(cm.Fee_Meituan_Booking, 0)) AS Fee_Meituan_Booking, SUM(ISNULL(cm.Fee_Meituan_GroupBuy, 0)) AS Fee_Meituan_GroupBuy,
            SUM(ISNULL(cm.Fee_Douyin_Booking, 0)) AS Fee_Douyin_Booking, SUM(ISNULL(cm.Fee_Douyin_GroupBuy, 0)) AS Fee_Douyin_GroupBuy,
            SUM(ISNULL(cm.Fee_Bank_GF, 0)) AS Fee_Bank_GF, SUM(ISNULL(cm.Fee_Bank_CITIC, 0)) AS Fee_Bank_CITIC, SUM(ISNULL(cm.Fee_Bank_UnionPay, 0)) AS Fee_Bank_UnionPay
        FROM BaseRecords br
        LEFT JOIN ChannelMetrics cm ON br.InvNo = cm.InvNo
    )
    UPDATE dbo.stg_Fact_Daily_Shop_Summary
    SET
        TotalRevenue = fm.TotalRevenue, DayTimeRevenue = fm.DayTimeRevenue, NightTimeRevenue = fm.NightTimeRevenue,
        TotalBatches = fm.TotalBatches, BuffetGuestCount = fm.BuffetGuestCount, TotalDirectFallGuests = fm.TotalDirectFallGuests,
        DayTimeBatchCount = fm.DayTimeBatchCount, NightTimeBatchCount = fm.NightTimeBatchCount, NightTimeGuestCount = fm.NightTimeGuestCount,
        ComplimentaryBatches = fm.ComplimentaryBatches,
        ComplimentaryRevenue = fm.ComplimentaryRevenue,
        AvgPricePerBill_Buffet = CASE WHEN fm.DayTimeBatchCount > 0 THEN fm.DayTimeRevenue / fm.DayTimeBatchCount ELSE 0 END,
        AvgPricePerGuest_Buffet = CASE WHEN fm.BuffetGuestCount > 0 THEN fm.DayTimeRevenue / fm.BuffetGuestCount ELSE 0 END,
        AvgPricePerGuest_PrimeTime = CASE WHEN fm.NightTimeGuestCount > 0 THEN fm.NightTimeRevenue / fm.NightTimeGuestCount ELSE 0 END,
        PrivilegeBooking_Count_0Yuan = fm.PrivilegeBooking_Count_0Yuan, PrivilegeBooking_Count_5Yuan = fm.PrivilegeBooking_Count_5Yuan,
        PrivilegeBooking_Count_10Yuan = fm.PrivilegeBooking_Count_10Yuan, PrivilegeBooking_Count_15Yuan = fm.PrivilegeBooking_Count_15Yuan,
        Revenue_PrivilegeBooking = fm.Revenue_PrivilegeBooking,
        Night_FreeMeal_Batches = fm.Night_FreeMeal_Batches, Night_FreeMeal_Revenue = fm.Night_FreeMeal_Revenue,
        Night_Buyout_Batches = fm.Night_Buyout_Batches, Night_DrinkPackage_Batches = fm.Night_DrinkPackage_Batches,
        Night_FreeConsumption_Batches = fm.Night_FreeConsumption_Batches, Night_RoomFee_Batches = fm.Night_RoomFee_Batches, 
        Night_Other_Batches = fm.Night_Other_Batches,
        Revenue_OfficialAccount = fm.Revenue_OfficialAccount,
        Revenue_Meituan_Booking = fm.Revenue_Meituan_Booking, Revenue_Meituan_GroupBuy = fm.Revenue_Meituan_GroupBuy,
        Revenue_Douyin_Booking = fm.Revenue_Douyin_Booking, Revenue_Douyin_GroupBuy = fm.Revenue_Douyin_GroupBuy,
        Revenue_Bank_GF = fm.Revenue_Bank_GF, Revenue_Bank_CITIC = fm.Revenue_Bank_CITIC, Revenue_Bank_UnionPay = fm.Revenue_Bank_UnionPay,
        Fee_Meituan_Booking = fm.Fee_Meituan_Booking, Fee_Meituan_GroupBuy = fm.Fee_Meituan_GroupBuy,
        Fee_Douyin_Booking = fm.Fee_Douyin_Booking, Fee_Douyin_GroupBuy = fm.Fee_Douyin_GroupBuy,
        Fee_Bank_GF = fm.Fee_Bank_GF, Fee_Bank_CITIC = fm.Fee_Bank_CITIC, Fee_Bank_UnionPay = fm.Fee_Bank_UnionPay,
        Revenue_Meituan = fm.Revenue_Meituan_Booking + fm.Revenue_Meituan_GroupBuy,
        Revenue_Douyin = fm.Revenue_Douyin_Booking + fm.Revenue_Douyin_GroupBuy,
        Revenue_Bank = fm.Revenue_Bank_GF + fm.Revenue_Bank_CITIC + fm.Revenue_Bank_UnionPay,
        Fee_Bank_Total = fm.Fee_Bank_GF + fm.Fee_Bank_CITIC + fm.Fee_Bank_UnionPay,
        UpdateTime = GETDATE()
    FROM FinalMetrics fm
    WHERE DateSK = @DateSK AND ShopSK = @ShopSK;

END
