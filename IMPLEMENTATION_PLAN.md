
# 实施计划：填充每日汇总表中的平台手续费

**版本**: 1.0
**目标**: 完成 `Fact_Daily_Shop_Summary` 表中所有平台手续费相关字段 (`Fee_*`) 的ETL填充逻辑。

---

## 阶段 1: 源数据调查

- **目标**: 识别并确认包含美团、抖音、银行等手续费的源数据表和字段。
- **成功标准**: 获得一个明确的、包含服务器、数据库、表名和字段名的列表，这些是计算手续费的数据来源。
- **测试**: 对识别出的源表进行查询，确认其中包含了可用于计算手续费的真实数据。
- **状态**: **未开始**

## 阶段 2: ETL 逻辑实现

- **目标**: 创建一个新的专用计算存储过程 `usp_Calculate_Fees_ForStaging`。
- **成功标准**: 该存储过程能够根据阶段1中找到的源数据，为指定的门店和日期计算出所有手续费，并成功更新到 `stg_Fact_Daily_Shop_Summary` 暂存表中。
- **测试**: 独立执行 `usp_Calculate_Fees_ForStaging`，并验证 `stg_Fact_Daily_Shop_Summary` 表中的对应字段是否被正确更新。
- **状态**: 未开始

## 阶段 3: 集成与验证

- **目标**: 将新的手续费计算过程集成到现有的ETL主流程中。
- **成功标准**: `usp_Populate_Fact_Daily_Shop_Summary` 主过程能够成功调用 `usp_Calculate_Fees_ForStaging`，并将手续费数据正确地合并到最终的 `Fact_Daily_Shop_Summary` 表中。
- **测试**: 完整执行 `usp_Job_Run_Daily_ETL`，然后查询 `Fact_Daily_Shop_Summary` 表，验证所有手续费字段及其他字段是否都已正确填充。
- **状态**: 未开始
