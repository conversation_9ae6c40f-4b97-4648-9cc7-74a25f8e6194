import pyodbc
import os

# --- Configuration ---
SERVER = '192.168.2.5'
DATABASE = 'operatedata'
USERNAME = 'sa'
PASSWORD = 'Musicbox123'

# --- V5 Trigger SQL (Final version with LogID, WorkDate handling) ---
# Batch 1: Drop existing trigger if it exists
sql_batch_1 = """
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[trg_InsteadOfInsert_RoomStatisticsHourly]'))
    DROP TRIGGER [dbo].[trg_InsteadOfInsert_RoomStatisticsHourly];
"""

# Batch 2: Create the new, final, ultimate trigger
sql_batch_2 = """
CREATE TRIGGER [dbo].[trg_InsteadOfInsert_RoomStatisticsHourly]
ON [dbo].[RoomStatisticsHourly]
INSTEAD OF INSERT
AS
BEGIN
    SET NOCOUNT ON;

    -- Declare variables for cursor
    DECLARE @LogID INT; DECLARE @ShopID INT; DECLARE @LogTime DATETIME; DECLARE @TotalRoomsBeforeFilter INT; 
    DECLARE @ValidRoomsCount INT; DECLARE @BadRoomsCount INT; DECLARE @AvailableRoomsCount INT; 
    DECLARE @Status_A_Count INT; DECLARE @Status_B_Count INT; DECLARE @Status_E_Count INT; DECLARE @Status_U_Count INT;

    DECLARE cur CURSOR LOCAL FORWARD_ONLY FOR
    SELECT 
        -- If LogID is not provided by the insert, generate one from the PK hash
        ISNULL(LogID, CHECKSUM(ShopID, LogTime)),
        ShopID, LogTime, TotalRoomsBeforeFilter, ValidRoomsCount, BadRoomsCount, AvailableRoomsCount, 
        Status_A_Count, Status_B_Count, Status_E_Count, Status_U_Count
    FROM inserted;

    OPEN cur;

    FETCH NEXT FROM cur INTO 
        @LogID, @ShopID, @LogTime, @TotalRoomsBeforeFilter, @ValidRoomsCount, @BadRoomsCount, @AvailableRoomsCount,
        @Status_A_Count, @Status_B_Count, @Status_E_Count, @Status_U_Count;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- Check if the record already exists
        IF EXISTS (SELECT 1 FROM dbo.RoomStatisticsHourly WHERE ShopID = @ShopID AND LogTime = @LogTime)
        BEGIN
            -- If it exists, UPDATE it. Do not update LogID, just the data.
            UPDATE dbo.RoomStatisticsHourly
            SET
                TotalRoomsBeforeFilter = @TotalRoomsBeforeFilter,
                ValidRoomsCount = @ValidRoomsCount,
                BadRoomsCount = @BadRoomsCount,
                AvailableRoomsCount = @AvailableRoomsCount,
                Status_A_Count = @Status_A_Count,
                Status_B_Count = @Status_B_Count,
                Status_E_Count = @Status_E_Count,
                Status_U_Count = @Status_U_Count,
                WorkDate = CONVERT(DATE, @LogTime)
            WHERE
                ShopID = @ShopID AND LogTime = @LogTime;
        END
        ELSE
        BEGIN
            -- If it does not exist, INSERT it.
            INSERT INTO dbo.RoomStatisticsHourly (
                LogID, ShopID, LogTime, WorkDate, TotalRoomsBeforeFilter, ValidRoomsCount, BadRoomsCount, AvailableRoomsCount,
                Status_A_Count, Status_B_Count, Status_E_Count, Status_U_Count
            )
            VALUES (
                @LogID, @ShopID, @LogTime, CONVERT(DATE, @LogTime), @TotalRoomsBeforeFilter, @ValidRoomsCount, @BadRoomsCount, @AvailableRoomsCount,
                @Status_A_Count, @Status_B_Count, @Status_E_Count, @Status_U_Count
            );
        END

        FETCH NEXT FROM cur INTO 
            @LogID, @ShopID, @LogTime, @TotalRoomsBeforeFilter, @ValidRoomsCount, @BadRoomsCount, @AvailableRoomsCount,
            @Status_A_Count, @Status_B_Count, @Status_E_Count, @Status_U_Count;
    END

    CLOSE cur;
    DEALLOCATE cur;
END;
""";

# --- Execution Logic ---
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;'
cxn = None
print(f"Connecting to {SERVER}/{DATABASE} to deploy FINAL trigger (v5)...")
try:
    cnxn = pyodbc.connect(conn_str, autocommit=True)
    cursor = cnxn.cursor()
    print("Connection successful.")

    print("Executing Batch 1: DROP TRIGGER if exists...")
    cursor.execute(sql_batch_1)
    print("Batch 1 executed.")

    print("Executing Batch 2: CREATE FINAL TRIGGER...")
    cursor.execute(sql_batch_2)
    print("Batch 2 executed.")

    print("\nFINAL Trigger (v5) created successfully via Python script!")

except pyodbc.Error as ex:
    print(f"\nA database error occurred: {ex}")
except Exception as ex:
    print(f"\nAn unexpected error occurred: {ex}")
finally:
    if cnxn:
        cnxn.close()
        print("Connection closed.")
