import pyodbc
import pandas as pd

# Connection details from memory
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'

sp_name = 'usp_Populate_Fact_Daily_BatchSize_Summary'

print(f"Attempting to connect to database '{database}' on server '{server}'...")

try:
    with pyodbc.connect(conn_str) as conn:
        print("Connection successful.")
        
        # Use sp_helptext to get the definition
        sql_query = f"EXEC sp_helptext '{sp_name}'"
        
        print(f"Fetching definition for stored procedure: {sp_name}")
        
        # Execute the query and fetch results into a DataFrame
        df = pd.read_sql(sql_query, conn)
        
        if not df.empty:
            print(f"\n--- Definition for {sp_name} ---")
            # Concatenate all rows of the 'Text' column to get the full SP definition
            sp_definition = ''.join(df['Text'])
            print(sp_definition)
        else:
            print(f"Stored procedure '{sp_name}' not found or has no definition.")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error Occurred: {sqlstate}")
    print(ex)

except Exception as e:
    print(f"An unexpected error occurred: {e}")
