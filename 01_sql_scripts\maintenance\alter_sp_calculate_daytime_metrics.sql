/*
Recreates the usp_Calculate_DayTimeMetrics_ForStaging procedure to include
detailed channel revenue and fee calculations.
*/

IF OBJECT_ID('usp_Calculate_DayTimeMetrics_ForStaging', 'P') IS NOT NULL
    DROP PROCEDURE usp_Calculate_DayTimeMetrics_ForStaging;
GO

CREATE PROCEDURE dbo.usp_Calculate_DayTimeMetrics_ForStaging
    @ShopId INT,
    @TargetDate DATE,
    @ShopSK INT,
    @DateSK INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Use a fixed date range for the business day (e.g., 08:00 AM to next day 06:00 AM)
    DECLARE @BeginDate DATETIME = DATEADD(hour, 8, CAST(@TargetDate AS DATETIME));
    DECLARE @EndDate DATETIME = DATEADD(hour, 6, CAST(DATEADD(day, 1, @TargetDate) AS DATETIME));

    -- CTE to get all relevant transactions and their channel information
    WITH TransactionsWithChannel AS (
        SELECT
            r.InvNo,
            r.<PERSON>,
            r.<PERSON>,
            r.<PERSON>,
            r.<PERSON>,
            r.<PERSON>,
            r.<PERSON>,
            r.<PERSON>ff<PERSON>ay,
            -- Add other base revenue/payment fields from rmcloseinfo as needed
            b.FdNo,
            b.FdQty,
            b.CashType,
            ddm.FdPrice2,
            dc.ChannelName,
            dc.SubChannelName,
            -- Placeholder for FeeRate, assuming it will be added to Dim_Deal_Map
            -- For now, using a fixed rate for demonstration.
            ISNULL(ddm.FeeRate, 0.006) as FeeRate 
        FROM dbo.RmCloseInfo AS r
        LEFT JOIN dbo.FdCashBak AS b 
            ON r.InvNo = b.InvNo AND r.Shopid = b.Shopid
        LEFT JOIN dbo.Dim_Deal_Map AS ddm 
            ON b.ShopId = ddm.ShopId AND b.FdNo = ddm.FdNo COLLATE Chinese_PRC_Stroke_CI_AS
        LEFT JOIN dbo.Dim_Channel AS dc 
            ON ddm.ChannelSK = dc.ChannelSK
        WHERE 
            r.Shopid = @ShopId 
            AND r.CloseDatetime BETWEEN @BeginDate AND @EndDate
    ),
    -- CTE to calculate all metrics in one go
    CalculatedMetrics AS (
        SELECT
            -- Existing operational metrics (simplified for clarity, can be expanded)
            SUM(CASE WHEN t.CashType = 'N' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS TotalRevenue, -- Example Total Revenue
            COUNT(DISTINCT t.InvNo) AS TotalBatches,
            SUM(t.Numbers) AS BuffetGuestCount, -- Example Guest Count
            SUM(CASE WHEN t.IsDirectFall = 1 THEN t.Numbers ELSE 0 END) AS TotalDirectFallGuests,

            -- Detailed Channel Revenue
            SUM(CASE WHEN t.ChannelName = N'美团' AND t.SubChannelName = N'美预' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Meituan_Booking,
            SUM(CASE WHEN t.ChannelName = N'美团' AND t.SubChannelName = N'美团' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Meituan_GroupBuy,
            SUM(CASE WHEN t.ChannelName = N'抖音' AND t.SubChannelName = N'抖预' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Douyin_Booking,
            SUM(CASE WHEN t.ChannelName = N'抖音' AND t.SubChannelName = N'抖音' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Douyin_GroupBuy,
            SUM(CASE WHEN t.SubChannelName = N'广发银行' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Bank_GF,
            SUM(CASE WHEN t.SubChannelName = N'中信银行' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Bank_CITIC,
            SUM(CASE WHEN t.SubChannelName = N'广日银联' THEN t.FdPrice2 * t.FdQty ELSE 0 END) AS Revenue_Bank_UnionPay,

            -- Detailed Channel Fees
            SUM(CASE WHEN t.ChannelName = N'美团' AND t.SubChannelName = N'美预' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Meituan_Booking,
            SUM(CASE WHEN t.ChannelName = N'美团' AND t.SubChannelName = N'美团' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Meituan_GroupBuy,
            SUM(CASE WHEN t.ChannelName = N'抖音' AND t.SubChannelName = N'抖预' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Douyin_Booking,
            SUM(CASE WHEN t.ChannelName = N'抖音' AND t.SubChannelName = N'抖音' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Douyin_GroupBuy,
            SUM(CASE WHEN t.SubChannelName = N'广发银行' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Bank_GF,
            SUM(CASE WHEN t.SubChannelName = N'中信银行' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Bank_CITIC,
            SUM(CASE WHEN t.SubChannelName = N'广日银联' THEN t.FdPrice2 * t.FdQty * t.FeeRate ELSE 0 END) AS Fee_Bank_UnionPay,

            -- Other revenues from rmcloseinfo
            (SELECT SUM(WechatOfficialPay) FROM dbo.RmCloseInfo WHERE Shopid = @ShopId AND CloseDatetime BETWEEN @BeginDate AND @EndDate) AS Revenue_OfficialAccount
            -- PrivilegeBooking revenue logic would be added here based on its source

        FROM TransactionsWithChannel t
    )
    -- Final update to the staging table
    UPDATE dbo.stg_Fact_Daily_Shop_Summary
    SET
        TotalRevenue = ISNULL(cm.TotalRevenue, 0),
        TotalBatches = ISNULL(cm.TotalBatches, 0),
        BuffetGuestCount = ISNULL(cm.BuffetGuestCount, 0),
        TotalDirectFallGuests = ISNULL(cm.TotalDirectFallGuests, 0),
        
        -- Set Channel Revenues
        Revenue_Meituan_Booking = ISNULL(cm.Revenue_Meituan_Booking, 0),
        Revenue_Meituan_GroupBuy = ISNULL(cm.Revenue_Meituan_GroupBuy, 0),
        Revenue_Douyin_Booking = ISNULL(cm.Revenue_Douyin_Booking, 0),
        Revenue_Douyin_GroupBuy = ISNULL(cm.Revenue_Douyin_GroupBuy, 0),
        Revenue_Bank_GF = ISNULL(cm.Revenue_Bank_GF, 0),
        Revenue_Bank_CITIC = ISNULL(cm.Revenue_Bank_CITIC, 0),
        Revenue_Bank_UnionPay = ISNULL(cm.Revenue_Bank_UnionPay, 0),
        Revenue_OfficialAccount = ISNULL(cm.Revenue_OfficialAccount, 0),

        -- Set Channel Fees
        Fee_Meituan_Booking = ISNULL(cm.Fee_Meituan_Booking, 0),
        Fee_Meituan_GroupBuy = ISNULL(cm.Fee_Meituan_GroupBuy, 0),
        Fee_Douyin_Booking = ISNULL(cm.Fee_Douyin_Booking, 0),
        Fee_Douyin_GroupBuy = ISNULL(cm.Fee_Douyin_GroupBuy, 0),
        Fee_Bank_GF = ISNULL(cm.Fee_Bank_GF, 0),
        Fee_Bank_CITIC = ISNULL(cm.Fee_Bank_CITIC, 0),
        Fee_Bank_UnionPay = ISNULL(cm.Fee_Bank_UnionPay, 0),

        -- Set aggregated revenues
        Revenue_Meituan = ISNULL(cm.Revenue_Meituan_Booking, 0) + ISNULL(cm.Revenue_Meituan_GroupBuy, 0),
        Revenue_Douyin = ISNULL(cm.Revenue_Douyin_Booking, 0) + ISNULL(cm.Revenue_Douyin_GroupBuy, 0),
        Revenue_Bank = ISNULL(cm.Revenue_Bank_GF, 0) + ISNULL(cm.Revenue_Bank_CITIC, 0) + ISNULL(cm.Revenue_Bank_UnionPay, 0),
        Fee_Bank_Total = ISNULL(cm.Fee_Bank_GF, 0) + ISNULL(cm.Fee_Bank_CITIC, 0) + ISNULL(cm.Fee_Bank_UnionPay, 0),

        UpdateTime = GETDATE()
    FROM CalculatedMetrics cm
    WHERE DateSK = @DateSK AND ShopSK = @ShopSK;

END;
GO
