CREATE OR ALTER PROCEDURE usp_Populate_Fact_Daily_BatchSize_Summary
    @RunDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    DELETE FROM dbo.Fact_Daily_BatchSize_Summary
    WHERE WorkDate = @RunDate;

    INSERT INTO dbo.Fact_Daily_BatchSize_Summary (
        WorkDate, ShopId, Category, TotalBatches, TotalAmount,
        Batches_2_Person, Amount_2_Person,
        Batches_3_Person, Amount_3_Person,
        Batches_4_Person, Amount_4_Person,
        Batches_5_Person, Amount_5_Person,
        Batches_6_Person, Amount_6_Person,
        Batches_7_Person, Amount_7_Person,
        Batches_8_Person, Amount_8_Person,
        Batches_9_Person, Amount_9_Person,
        Batches_10_Plus_Person, Amount_10_Plus_Person
    )
    WITH BillSourceData AS (
        SELECT
            rci.ShopId,
            rci.InvNo,
            rci.OpenDateTime,
            fd.FdQty AS GuestCount,
            (
                rci.Cash + rci.Cash_Targ * 0.8 + rci.Vesa + rci.GZ + rci.AccOkZD + 
                rci.RechargeAccount + rci.NoPayed + rci.WXPay + rci.AliPay + rci.MTPay + 
                rci.DZPay + rci.NMPay + rci.[Check] + rci.WechatDeposit + rci.WechatShopping + 
                rci.ReturnAccount + ISNULL(rci.WechatOfficialPay, 0)
            ) AS BillAmount
        FROM
            operatedata.dbo.rmcloseinfo AS rci
        JOIN
            operatedata.dbo.fdcashbak AS fd ON rci.InvNo = fd.InvNo COLLATE DATABASE_DEFAULT
        WHERE
            rci.WorkDate = FORMAT(@RunDate, 'yyyyMMdd')
            AND fd.FdCName = N'消费人数'
            AND fd.CashType = 'N'
            AND rci.OpenDateTime IS NOT NULL
    ),
    BillWithCategory AS (
        SELECT
            ShopId,
            InvNo,
            GuestCount,
            BillAmount,
            CASE 
                WHEN CAST(OpenDateTime AS TIME) < '20:00:00' THEN N'自助餐' 
                ELSE N'黄金档' 
            END AS Category
        FROM
            BillSourceData
    )
    SELECT
        @RunDate,
        ShopId,
        Category,
        COUNT(InvNo) AS TotalBatches,
        SUM(BillAmount) AS TotalAmount,
        COUNT(CASE WHEN GuestCount = 2 THEN 1 END) AS Batches_2_Person,
        SUM(CASE WHEN GuestCount = 2 THEN BillAmount ELSE 0 END) AS Amount_2_Person,
        COUNT(CASE WHEN GuestCount = 3 THEN 1 END) AS Batches_3_Person,
        SUM(CASE WHEN GuestCount = 3 THEN BillAmount ELSE 0 END) AS Amount_3_Person,
        COUNT(CASE WHEN GuestCount = 4 THEN 1 END) AS Batches_4_Person,
        SUM(CASE WHEN GuestCount = 4 THEN BillAmount ELSE 0 END) AS Amount_4_Person,
        COUNT(CASE WHEN GuestCount = 5 THEN 1 END) AS Batches_5_Person,
        SUM(CASE WHEN GuestCount = 5 THEN BillAmount ELSE 0 END) AS Amount_5_Person,
        COUNT(CASE WHEN GuestCount = 6 THEN 1 END) AS Batches_6_Person,
        SUM(CASE WHEN GuestCount = 6 THEN BillAmount ELSE 0 END) AS Amount_6_Person,
        COUNT(CASE WHEN GuestCount = 7 THEN 1 END) AS Batches_7_Person,
        SUM(CASE WHEN GuestCount = 7 THEN BillAmount ELSE 0 END) AS Amount_7_Person,
        COUNT(CASE WHEN GuestCount = 8 THEN 1 END) AS Batches_8_Person,
        SUM(CASE WHEN GuestCount = 8 THEN BillAmount ELSE 0 END) AS Amount_8_Person,
        COUNT(CASE WHEN GuestCount = 9 THEN 1 END) AS Batches_9_Person,
        SUM(CASE WHEN GuestCount = 9 THEN BillAmount ELSE 0 END) AS Amount_9_Person,
        COUNT(CASE WHEN GuestCount >= 10 THEN 1 END) AS Batches_10_Plus_Person,
        SUM(CASE WHEN GuestCount >= 10 THEN BillAmount ELSE 0 END) AS Amount_10_Plus_Person
    FROM
        BillWithCategory
    GROUP BY
        ShopId,
        Category;

    SET NOCOUNT OFF;
END;
GO