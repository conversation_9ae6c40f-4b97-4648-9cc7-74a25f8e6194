
import pyodbc
import pandas as pd

# --- Configuration ---
DB_SERVER = '192.168.2.5'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'

def audit_phantom_data():
    """
    Audits all records with NonPackage_RoomFee <= 0 to find the root cause of discrepancies.
    """
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
    
    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        print("数据库连接成功。\n")

        # 1. Get all problematic cases
        problem_cases_sql = """
        SELECT 
            h.ReportID, h.ShopID, h.ReportDate,
            nd.Buyout_BatchCount AS Stored_Buyout_Count,
            nd.Changyin_BatchCount AS Stored_Changyin_Count
        FROM 
            dbo.FullDailyReport_NightDetails nd
        JOIN 
            dbo.FullDailyReport_Header h ON nd.ReportID = h.ReportID
        WHERE 
            nd.NonPackage_RoomFee <= 0
        ORDER BY
            h.ReportID;
        """
        print("正在查询所有 NonPackage_RoomFee <= 0 的案例...")
        problem_cases_df = pd.read_sql(problem_cases_sql, cnxn)
        print(f"找到 {len(problem_cases_df)} 个需要审计的案例。\n")

        # 2. Loop through each case and get the ground truth
        for index, row in problem_cases_df.iterrows():
            report_id = row['ReportID']
            shop_id = row['ShopID']
            report_date = row['ReportDate']
            stored_buyout = row['Stored_Buyout_Count']
            stored_changyin = row['Stored_Changyin_Count']

            ground_truth_sql = f"""
            WITH NightTimeInvoices AS (
                SELECT DISTINCT rt.InvNo
                FROM dbo.RmCloseInfo AS rt
                LEFT JOIN dbo.shoptimeinfo AS sti ON rt.Shopid = sti.Shopid AND rt.Beg_Key = sti.TimeNo
                WHERE rt.Shopid = {shop_id}
                  AND rt.CloseDatetime BETWEEN DATEADD(hour, 8, CAST('{report_date}' AS DATETIME)) AND DATEADD(hour, 6, CAST(DATEADD(day, 1, '{report_date}') AS DATETIME))
                  AND (CASE WHEN sti.TimeMode IS NOT NULL THEN sti.TimeMode WHEN rt.OpenDateTime IS NOT NULL AND DATEPART(hour, rt.OpenDateTime) >= 20 THEN 2 WHEN DATEPART(hour, rt.CloseDatetime) >= 20 THEN 2 ELSE 1 END) = 2
            )
            SELECT 
                ISNULL(COUNT(DISTINCT CASE WHEN fdc.FdCName LIKE N'%买断%' THEN fdc.InvNo END), 0) AS Actual_Buyout_Count,
                ISNULL(COUNT(DISTINCT CASE WHEN fdc.FdCName LIKE N'%畅饮%' THEN fdc.InvNo END), 0) AS Actual_Changyin_Count
            FROM 
                NightTimeInvoices nti
            JOIN 
                operatedata.dbo.FdCashBak fdc ON nti.InvNo = fdc.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE fdc.ShopId = {shop_id};
            """
            
            ground_truth_df = pd.read_sql(ground_truth_sql, cnxn)
            actual_buyout = ground_truth_df.loc[0, 'Actual_Buyout_Count']
            actual_changyin = ground_truth_df.loc[0, 'Actual_Changyin_Count']

            # 3. Compare and report
            if stored_buyout != actual_buyout or stored_changyin != actual_changyin:
                print("----------------------------------------------------------------")
                print(f"发现不一致: ReportID = {report_id} (ShopID: {shop_id}, Date: {report_date})")
                print(f"  - 买断批次: 存储值 = {stored_buyout}, 原始数据计算值 = {actual_buyout}")
                print(f"  - 畅饮批次: 存储值 = {stored_changyin}, 原始数据计算值 = {actual_changyin}")
                if actual_buyout == 0 and actual_changyin == 0:
                    print("  - 结论: 存储的买断/畅饮批次是‘幽灵数据’，原始数据中不存在。")
                else:
                    print("  - 结论: 存储值与原始数据计算值不符，旧的计算逻辑存在错误。")
        
        print("\n----------------------------------------------------------------")
        print("审计完成。")

    except Exception as e:
        print(f"执行出错: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    audit_phantom_data()
