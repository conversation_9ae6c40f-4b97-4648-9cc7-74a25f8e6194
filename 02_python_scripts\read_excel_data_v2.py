
import openpyxl
import os

def read_excel_data_safely():
    """
    Reads data from a specified Excel file using openpyxl in data_only mode
    to avoid issues with complex styling.
    """
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        file_path = os.path.join(project_root, '营业数据统计平台开发需求 (1).xlsx')

        print(f"正在以安全模式读取文件: {file_path}\n")

        # Load the workbook in data_only mode to ignore styles and formulas
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        
        # Get the active sheet
        sheet = workbook.active

        print("--- Excel 文件内容 ---")
        # Iterate over all rows in the sheet and print the cell values
        for row in sheet.iter_rows():
            # Join cell values into a string, handling None values
            row_values = [str(cell.value) if cell.value is not None else '' for cell in row]
            print("\t".join(row_values))
        
        print("\n--- 读取完毕 ---")

    except FileNotFoundError:
        print(f"错误: 文件未找到，请确认路径是否正确: {file_path}")
    except Exception as e:
        print(f"发生错误: {e}")
        print("\n请确保您已经安装了openpyxl库。")
        print("您可以使用此命令安装: pip install openpyxl")

if __name__ == '__main__':
    read_excel_data_safely()
