/*
Adds detailed channel revenue and fee columns to the daily summary fact tables.
*/

-- Add columns to the staging table
PRINT 'Altering stg_Fact_Daily_Shop_Summary...';
ALTER TABLE stg_Fact_Daily_Shop_Summary
ADD
    Revenue_OfficialAccount DECIMAL(18, 2) NULL,
    Revenue_PrivilegeBooking DECIMAL(18, 2) NULL,
    Revenue_Meituan DECIMAL(18, 2) NULL,
    Revenue_Douyin DECIMAL(18, 2) NULL,
    Revenue_Bank DECIMAL(18, 2) NULL,
    Revenue_Meituan_Booking DECIMAL(18, 2) NULL,
    Revenue_Meituan_GroupBuy DECIMAL(18, 2) NULL,
    Revenue_Douyin_Booking DECIMAL(18, 2) NULL,
    Revenue_Douyin_GroupBuy DECIMAL(18, 2) NULL,
    Revenue_Bank_GF DECIMAL(18, 2) NULL,
    Revenue_Bank_CITIC DECIMAL(18, 2) NULL,
    Revenue_Bank_UnionPay DECIMAL(18, 2) NULL,
    Fee_Bank_Total DECIMAL(18, 2) NULL;
GO

-- Add the same columns to the final fact table
PRINT 'Altering Fact_Daily_Shop_Summary...';
ALTER TABLE Fact_Daily_Shop_Summary
ADD
    Revenue_OfficialAccount DECIMAL(18, 2) NULL,
    Revenue_PrivilegeBooking DECIMAL(18, 2) NULL,
    Revenue_Meituan DECIMAL(18, 2) NULL,
    Revenue_Douyin DECIMAL(18, 2) NULL,
    Revenue_Bank DECIMAL(18, 2) NULL,
    Revenue_Meituan_Booking DECIMAL(18, 2) NULL,
    Revenue_Meituan_GroupBuy DECIMAL(18, 2) NULL,
    Revenue_Douyin_Booking DECIMAL(18, 2) NULL,
    Revenue_Douyin_GroupBuy DECIMAL(18, 2) NULL,
    Revenue_Bank_GF DECIMAL(18, 2) NULL,
    Revenue_Bank_CITIC DECIMAL(18, 2) NULL,
    Revenue_Bank_UnionPay DECIMAL(18, 2) NULL,
    Fee_Bank_Total DECIMAL(18, 2) NULL;
GO

PRINT 'Table structures altered successfully.';
GO
