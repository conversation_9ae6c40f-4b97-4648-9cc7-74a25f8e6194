
-- =================================================================
-- 创建月度门店汇总事实表 Fact_Monthly_Shop_Summary
-- 基于设计文档，并根据报表图片需求增加了细分的收入字段
-- =================================================================
USE operatedata;
GO

IF OBJECT_ID('dbo.Fact_Monthly_Shop_Summary', 'U') IS NOT NULL
BEGIN
    PRINT N' -> 表 Fact_Monthly_Shop_Summary 已存在，跳过创建。';
END
ELSE
BEGIN
    CREATE TABLE dbo.Fact_Monthly_Shop_Summary (
        MonthlySummarySK              BIGINT IDENTITY(1,1) NOT NULL,
        Year                          INT NOT NULL,
        Month                         INT NOT NULL,
        ShopSK                        INT NOT NULL,
        -- 整体营收
        TotalRevenue                  DECIMAL(18, 2) NULL,
        TotalRevenue_MoM              DECIMAL(18, 4) NULL,
        TotalRevenue_YoY              DECIMAL(18, 4) NULL,
        -- 收入大项组成 (根据图片细化)
        Revenue_Offline               DECIMAL(18, 2) NULL, -- 线下门店营收
        Revenue_Privilege             DECIMAL(18, 2) NULL, -- 特权预约
        Revenue_WeChat                DECIMAL(18, 2) NULL, -- 公众号
        Revenue_Meituan_Booking       DECIMAL(18, 2) NULL, -- 新增: 美团预约
        Revenue_Meituan_GroupBuy      DECIMAL(18, 2) NULL, -- 新增: 美团团购
        Revenue_Douyin_Booking        DECIMAL(18, 2) NULL, -- 新增: 抖音预约
        Revenue_Douyin_GroupBuy       DECIMAL(18, 2) NULL, -- 新增: 抖音团购
        Revenue_Bank_GF               DECIMAL(18, 2) NULL, -- 新增: 广发银行
        Revenue_Bank_CITIC            DECIMAL(18, 2) NULL, -- 新增: 中信银行
        Revenue_Bank_UnionPay         DECIMAL(18, 2) NULL, -- 新增: 银联
        Revenue_Other                 DECIMAL(18, 2) NULL, -- 其他异业
        -- 特权预约执行次数
        PrivilegeBooking_Count_0Yuan  INT NULL,
        PrivilegeBooking_Count_5Yuan  INT NULL,
        PrivilegeBooking_Count_10Yuan INT NULL,
        PrivilegeBooking_Count_15Yuan INT NULL,
        -- 平台手续费
        Fee_Meituan_Booking           DECIMAL(18, 2) NULL,
        Fee_Meituan_GroupBuy          DECIMAL(18, 2) NULL,
        Fee_Douyin_Booking            DECIMAL(18, 2) NULL,
        Fee_Douyin_GroupBuy           DECIMAL(18, 2) NULL,
        Fee_Bank_GF                   DECIMAL(18, 2) NULL,
        Fee_Bank_CITIC                DECIMAL(18, 2) NULL,
        Fee_Bank_UnionPay             DECIMAL(18, 2) NULL,
        -- 其他汇总指标
        TotalBatches                  INT NULL,
        BuffetGuestCount              INT NULL,
        TotalDirectFallGuests         INT NULL,
        ComplimentaryBatches          INT NULL,
        ComplimentaryRevenue          DECIMAL(18, 2) NULL,
        -- 元数据
        CreateTime                    DATETIME NOT NULL DEFAULT GETDATE(),
        UpdateTime                    DATETIME NULL,
        OperatorId                    INT NULL,

        CONSTRAINT PK_Fact_Monthly_Shop_Summary PRIMARY KEY CLUSTERED (MonthlySummarySK ASC),
        CONSTRAINT UQ_Fact_Monthly_Shop_Summary UNIQUE (Year, Month, ShopSK)
    );
    PRINT N' -> 表 Fact_Monthly_Shop_Summary 创建成功。';
END
GO
