
import pyodbc
import pandas as pd

# Connection details - using memories
server = '192.168.2.5'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'

# Construct the connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'

table_name = 'FdCashBak'

print(f"Attempting to connect to database '{database}' on server '{server}'...")

try:
    # Establish connection
    with pyodbc.connect(conn_str) as conn:
        print("Connection successful.")
        
        # SQL query to get table schema information
        sql_query = f"""
        SELECT 
            c.name AS ColumnName,
            t.name AS DataType,
            c.max_length AS MaxLength,
            c.is_nullable AS IsNullable,
            CAST(ISNULL(p.value, '') AS NVARCHAR(MAX)) AS Description
        FROM 
            sys.columns c
        INNER JOIN 
            sys.tables tbl ON tbl.object_id = c.object_id
        INNER JOIN 
            sys.types t ON c.user_type_id = t.user_type_id
        LEFT JOIN
            sys.extended_properties p ON p.major_id = c.object_id AND p.minor_id = c.column_id AND p.name = 'MS_Description'
        WHERE 
            tbl.name = ?
        ORDER BY 
            c.column_id;
        """
        
        print(f"Fetching schema for table: {table_name}")
        # Execute the query
        df = pd.read_sql(sql_query, conn, params=[table_name])
        
        # Display the results
        if not df.empty:
            print(f"\n--- Schema for table: {table_name} ---")
            print(df.to_string())
        else:
            print(f"Table '{table_name}' not found or has no columns.")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database Error Occurred: {sqlstate}")
    print(ex)

except Exception as e:
    print(f"An unexpected error occurred: {e}")
