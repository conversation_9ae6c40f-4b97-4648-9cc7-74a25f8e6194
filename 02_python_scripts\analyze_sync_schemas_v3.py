'''
This script connects to the SQL Server database and retrieves the schema information 
for the source tables (openhistory, openhistory_bak on the linked server) and the 
target table (opencacheinfo on the local server) using INFORMATION_SCHEMA.
'''
import pyodbc

# Connection details from memory
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# Table definitions
TABLES_TO_ANALYZE = {
    "remote": [
        ("cloudRms2019.rms2019.dbo.openhistory", "openhistory"),
        ("cloudRms2019.rms2019.dbo.openhistory_bak", "openhistory_bak")
    ],
    "local": [
        ("operatedata.dbo.opencacheinfo", "opencacheinfo")
    ]
}

def print_schema_results(cursor, table_fqdn):
    """Prints query results in a formatted way."""
    print(f"--- Schema for: {table_fqdn} ---")
    try:
        results = cursor.fetchall()
        if not results:
            print("No column information found.")
            return

        cols_description = [desc[0] for desc in cursor.description]
        print("\t".join(cols_description))
        print("-" * 80)

        for row in results:
            print("\t".join(str(x).strip() if x is not None else 'NULL' for x in row))
    except pyodbc.Error as ex:
        print(f"Error fetching results for {table_fqdn}: {ex}")
    finally:
        print(f"--- End of Schema for: {table_fqdn} ---")
        print("=" * 80)
        print()

def main():
    """Main function to connect to DB and analyze schemas."""
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()

        # Analyze remote tables
        for fqdn, table_name in TABLES_TO_ANALYZE["remote"]:
            sql = f"""SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE 
                     FROM cloudRms2019.rms2019.INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_NAME = ?"""
            cursor.execute(sql, table_name)
            print_schema_results(cursor, fqdn)

        # Analyze local tables
        for fqdn, table_name in TABLES_TO_ANALYZE["local"]:
            sql = f"""SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE 
                     FROM operatedata.INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_NAME = ?"""
            cursor.execute(sql, table_name)
            print_schema_results(cursor, fqdn)

    except pyodbc.Error as ex:
        print(f"Database operation failed: {ex}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("Connection closed.")

if __name__ == "__main__":
    main()
