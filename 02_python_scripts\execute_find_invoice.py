import pyodbc

# Database connection details
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\find_discrepancy_invoice.sql'

cnxn = None
cursor = None
try:
    with open(sql_file_path, 'r', encoding='utf-8-sig') as f:
        sql_query = f.read()

    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    cursor.execute(sql_query)
    
    row = cursor.fetchone()

    if row:
        # Save the invoice number to a file for the next step
        with open("C:\\Users\\<USER>\\CascadeProjects\\KTV_Data_Analysis\\01_sql_scripts\\tests_and_adhoc\\problem_invoice.txt", "w") as f_out:
            f_out.write(row[0])
        print(f"Found problematic invoice: {row[0]}")
    else:
        print("No invoice with discrepancy found for the given criteria.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
