
USE operatedata;
GO

IF OBJECT_ID('dbo.Fact_Daily_Shop_Summary', 'U') IS NOT NULL
BEGIN
    DROP TABLE dbo.Fact_Daily_Shop_Summary;
END
GO

CREATE TABLE dbo.Fact_Daily_Shop_Summary (
    ShopSummarySK BIGINT PRIMARY KEY IDENTITY(1,1),
    DateSK INT NOT NULL,
    ShopSK INT NOT NULL,

    -- General Revenue & Batches
    TotalRevenue DECIMAL(18, 2) DEFAULT 0,
    DayTimeRevenue DECIMAL(18, 2) DEFAULT 0,
    NightTimeRevenue DECIMAL(18, 2) DEFAULT 0,
    TotalBatches INT DEFAULT 0,
    BuffetGuestCount INT DEFAULT 0,
    TotalDirectFallGuests INT DEFAULT 0,
    ComplimentaryBatches INT DEFAULT 0,
    ComplimentaryRevenue DECIMAL(18, 2) DEFAULT 0,

    -- Privilege Booking
    PrivilegeBooking_Count_0Yuan INT DEFAULT 0,
    PrivilegeBooking_Count_5Yuan INT DEFAULT 0,
    PrivilegeBooking_Count_10Yuan INT DEFAULT 0,
    PrivilegeBooking_Count_15Yuan INT DEFAULT 0,

    -- Platform Fees
    Fee_Meituan_Booking DECIMAL(18, 2) DEFAULT 0,
    Fee_Meituan_GroupBuy DECIMAL(18, 2) DEFAULT 0,
    Fee_Douyin_Booking DECIMAL(18, 2) DEFAULT 0,
    Fee_Douyin_GroupBuy DECIMAL(18, 2) DEFAULT 0,

    -- Bank Fees
    Fee_Bank_GF DECIMAL(18, 2) DEFAULT 0,
    Fee_Bank_CITIC DECIMAL(18, 2) DEFAULT 0,
    Fee_Bank_UnionPay DECIMAL(18, 2) DEFAULT 0,

    -- Night Time Details
    Night_FreeMeal_Batches INT DEFAULT 0,
    Night_FreeMeal_Revenue DECIMAL(18, 2) DEFAULT 0,
    Night_Buyout_Batches INT DEFAULT 0,
    Night_DrinkPackage_Batches INT DEFAULT 0,
    Night_FreeConsumption_Batches INT DEFAULT 0,
    Night_RoomFee_Batches INT DEFAULT 0,
    Night_Other_Batches INT DEFAULT 0,

    -- Stored Value (Recharge) Metrics
    DailyRecharge_Total DECIMAL(18, 2) DEFAULT 0,
    DailyRecharge_Meituan DECIMAL(18, 2) DEFAULT 0,
    DailyRecharge_Meituan_Ratio DECIMAL(5, 4) DEFAULT 0,
    RechargeTier1_BatchCount INT DEFAULT 0,
    RechargeTier1_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier2_BatchCount INT DEFAULT 0,
    RechargeTier2_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier3_BatchCount INT DEFAULT 0,
    RechargeTier3_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier4_BatchCount INT DEFAULT 0,
    RechargeTier4_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier5_BatchCount INT DEFAULT 0,
    RechargeTier5_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier6_BatchCount INT DEFAULT 0,
    RechargeTier6_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier7_BatchCount INT DEFAULT 0,
    RechargeTier7_TotalAmount DECIMAL(18, 2) DEFAULT 0,
    RechargeTier8_BatchCount INT DEFAULT 0,
    RechargeTier8_TotalAmount DECIMAL(18, 2) DEFAULT 0,

    -- Metadata
    CreateTime DATETIME DEFAULT GETDATE(),
    UpdateTime DATETIME DEFAULT GETDATE(),
    OperatorId INT,

    -- Constraints
    CONSTRAINT UQ_Fact_Daily_Shop_Summary UNIQUE (DateSK, ShopSK),
    -- Assuming Dim_Date and Dim_Shop tables exist and have primary keys DateSK and ShopSK respectively.
    -- CONSTRAINT FK_Fact_Daily_Shop_Summary_Dim_Date FOREIGN KEY (DateSK) REFERENCES dbo.Dim_Date(DateSK),
    -- CONSTRAINT FK_Fact_Daily_Shop_Summary_Dim_Shop FOREIGN KEY (ShopSK) REFERENCES dbo.Dim_Shop(ShopSK)
);
GO

PRINT 'Table Fact_Daily_Shop_Summary created successfully.';
GO
