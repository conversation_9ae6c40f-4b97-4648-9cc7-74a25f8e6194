import pyodbc

# --- Configuration ---
db_connection_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_create_sp_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\create_sp_monthly_summary.sql'

# --- Execution ---
cnxn = None
cursor = None
try:
    cnxn = pyodbc.connect(db_connection_str)
    cursor = cnxn.cursor()

    print("Reading the ALTER PROCEDURE script...")
    with open(sql_create_sp_path, 'r', encoding='utf-8-sig') as f:
        sql_script = f.read()

    print("Altering the stored procedure usp_Populate_Fact_Monthly_Summary...")
    cursor.execute(sql_script)
    cnxn.commit()
    print("Stored procedure altered successfully.")

    print("\nNow, running the procedure for September 2025...")
    cursor.execute("EXEC dbo.usp_Populate_Fact_Monthly_Summary @TargetYear = 2025, @TargetMonth = 9")
    cnxn.commit()
    print("Monthly summary generation for September 2025 completed.")

    print("\n--- Final Results from Fact_Monthly_Shop_Summary for Sep 2025 ---")
    # Query and print the results
    sql_query_results = """
        SELECT 
            Year, Month, ShopID, 
            Revenue_Total, Revenue_Total_MoM, 
            Revenue_Offline, Revenue_Offline_MoM,
            Revenue_Meituan, Revenue_Meituan_MoM
        FROM dbo.Fact_Monthly_Shop_Summary 
        WHERE Year = 2025 AND Month = 9
        ORDER BY ShopID;
    """
    cursor.execute(sql_query_results)
    rows = cursor.fetchall()
    columns = [column[0] for column in cursor.description]

    header = ' | '.join(f'{col:<25}' for col in columns)
    print(header)
    print('-' * len(header))

    if rows:
        for row in rows:
            # Format MoM as percentage
            formatted_row_list = []
            for i, item in enumerate(row):
                # Check if the column name ends with _MoM
                if columns[i].endswith('_MoM'):
                    try:
                        # Format as percentage with 2 decimal places
                        formatted_item = f'{float(item)*100:.2f}%'
                    except (ValueError, TypeError):
                        formatted_item = str(item) # Keep as is if conversion fails
                else:
                    formatted_item = str(item)
                formatted_row_list.append(f'{formatted_item:<25}')
            print(' | '.join(formatted_row_list))
    else:
        print("No monthly summary data generated.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()