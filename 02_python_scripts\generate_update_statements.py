
import pyodbc
import pandas as pd

# --- Configuration ---
DB_SERVER = '***********'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
OUTPUT_SQL_FILE = 'C:/Users/<USER>/CascadeProjects/KTV_Data_Analysis/01_sql_scripts/maintenance/generated_bulk_update.sql'

def generate_update_script():
    """
    Generates a bulk SQL UPDATE script by recalculating values for each record.
    """
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
    
    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print("数据库连接成功。")

        # 1. Get all records to be updated
        print("正在获取所有报表头记录...")
        header_sql = "SELECT ReportID, ShopID, ReportDate FROM dbo.FullDailyReport_Header ORDER BY ReportID;"
        headers_df = pd.read_sql(header_sql, cnxn)
        print(f"找到 {len(headers_df)} 条记录需要处理。")

        all_update_statements = []
        all_update_statements.append("USE operatedata;")
        all_update_statements.append("GO")
        all_update_statements.append("PRINT '开始执行批量更新...';")
        all_update_statements.append("GO")

        # 2. Loop through each record
        for index, row in headers_df.iterrows():
            report_id = row['ReportID']
            shop_id = row['ShopID']
            report_date = row['ReportDate']

            # 3. Execute the fixed SP for each record
            sp_sql = f"EXEC dbo.usp_Util_CalculateSimplifiedMetrics @ShopId = {shop_id}, @BeginDate = '{report_date}', @EndDate = '{report_date}'"
            
            # Fetch SP result into a DataFrame
            # This is inefficient but necessary for this workaround
            result_df = pd.read_sql(sp_sql, cnxn)

            if not result_df.empty:
                new_room_fee = result_df.loc[0, 'NonPackage_RoomFee']
                new_verify_count = result_df.loc[0, 'Night_Verify_BatchCount']

                # 4. Generate the hardcoded UPDATE statement
                update_sql = (
                    f"UPDATE dbo.FullDailyReport_NightDetails "
                    f"SET NonPackage_RoomFee = {new_room_fee}, Night_Verify_BatchCount = {new_verify_count} "
                    f"WHERE ReportID = {report_id};"
                )
                all_update_statements.append(update_sql)
                if (index + 1) % 100 == 0:
                    print(f"已生成 {index + 1} 条 UPDATE 语句...")
            else:
                print(f"警告: ReportID {report_id} 未能从存储过程中获取到计算结果。")

        all_update_statements.append("PRINT '所有 UPDATE 语句已执行完毕。';")
        all_update_statements.append("GO")

        # 5. Write all statements to the output file
        with open(OUTPUT_SQL_FILE, 'w', encoding='utf-8') as f:
            f.write('\n'.join(all_update_statements))
        
        print(f"\n成功! 包含 {len(headers_df)} 条 UPDATE 语句的脚本已生成。")
        print(f"文件路径: {OUTPUT_SQL_FILE}")

    except Exception as e:
        print(f"执行出错: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    generate_update_script()
