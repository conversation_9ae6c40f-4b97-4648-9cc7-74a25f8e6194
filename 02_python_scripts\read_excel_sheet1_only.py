
import openpyxl
import os

# --- Configuration ---
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_excel_path = os.path.join(project_root, '重复歌曲数量 .xlsx')
output_text_path = os.path.join(project_root, '03_reports', 'songs_sheet1_only.txt')

# --- Main Execution ---
try:
    workbook = openpyxl.load_workbook(input_excel_path, data_only=True)
    
    # The user is most likely interested in the first sheet.
    first_sheet_name = workbook.sheetnames[0]
    sheet = workbook[first_sheet_name]
    
    song_count = 0
    with open(output_text_path, 'w', encoding='utf-8') as f_out:
        for row in sheet.iter_rows():
            # Don't write completely empty rows
            if all(cell.value is None for cell in row):
                continue
            f_out.write(",".join([str(cell.value) if cell.value is not None else '' for cell in row]) + '\n')
            song_count += 1

    print(f"成功从Sheet1读取 {song_count} 行数据，并保存到: {os.path.abspath(output_text_path)}")

except Exception as e:
    print(f"处理Excel文件时发生错误: {e}")
