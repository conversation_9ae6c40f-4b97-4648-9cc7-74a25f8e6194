
'''
This script compares the schemas of the source and destination tables 
involved in the daily open data sync process to identify data type mismatches.
'''
import pyodbc
import pandas as pd

# Connection details
server = '***********'
username = 'sa'
password = 'Musicbox123'

# Tables to inspect
tables = {
    "local_opencacheinfo": {
        "db": "operatedata",
        "schema_query": "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'opencacheinfo'"
    },
    "remote_opencacheinfo": {
        "db": "operatedata", # We connect to operatedata and use the linked server
        "schema_query": "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH FROM cloudRms2019.rms2019.INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'opencacheinfo'"
    },
    "remote_openhistory": {
        "db": "operatedata",
        "schema_query": "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH FROM cloudRms2019.rms2019.INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'openhistory'"
    }
}

all_schemas = {}

def get_schema(db, query, conn_str):
    try:
        cnxn = pyodbc.connect(conn_str)
        df = pd.read_sql(query, cnxn)
        cnxn.close()
        return df.set_index('COLUMN_NAME')
    except pyodbc.Error as ex:
        print(f"Failed to get schema from DB: {db}")
        print(ex)
        return None

print("--- Retrieving table schemas ---")

for name, info in tables.items():
    print(f"Fetching schema for {name} from database {info['db']}...")
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={info["db"]};UID={username};PWD={password};TrustServerCertificate=yes;'
    schema_df = get_schema(info['db'], info['schema_query'], conn_str)
    if schema_df is not None:
        all_schemas[name] = schema_df

if len(all_schemas) == 3:
    print("\n--- Schema Comparison ---")
    # Get the schema for the local table
    local_schema = all_schemas['local_opencacheinfo']
    remote_schema_opencache = all_schemas['remote_opencacheinfo']
    remote_schema_openhist = all_schemas['remote_openhistory']

    # Find all columns present in the local schema
    all_columns = set(local_schema.index)

    # Prepare data for a comparison table
    comparison_data = []
    for col in sorted(list(all_columns)):
        local_type = local_schema.loc[col, 'DATA_TYPE'] if col in local_schema.index else '-'
        remote_type_cache = remote_schema_opencache.loc[col, 'DATA_TYPE'] if col in remote_schema_opencache.index else '-'
        remote_type_hist = remote_schema_openhist.loc[col, 'DATA_TYPE'] if col in remote_schema_openhist.index else '-'
        
        # Highlight mismatches
        is_mismatch = not (local_type == remote_type_cache == remote_type_hist) and remote_type_cache != '-'
        if is_mismatch:
            comparison_data.append([f"**{col}**", f"**{local_type}**", f"**{remote_type_cache}**", f"**{remote_type_hist}**"])
        else:
            comparison_data.append([col, local_type, remote_type_cache, remote_type_hist])

    # Use pandas to create a formatted table
    comparison_df = pd.DataFrame(comparison_data, columns=['Column', 'Local (operatedata.opencacheinfo)', 'Remote (opencacheinfo)', 'Remote (openhistory)'])
    
    print(comparison_df.to_markdown(index=False))

    print("\n**Analysis:** Mismatched data types are highlighted with asterisks (**). These are the most likely cause of the conversion error.")

else:
    print("Could not retrieve all schemas. Comparison aborted.")
