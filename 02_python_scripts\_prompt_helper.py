import json
import sys
import os

def main():
    config_path = 'prompts.json'

    if not os.path.exists(config_path):
        print(f"错误: 配置文件 '{config_path}' 未找到。")
        sys.exit(1)

    with open(config_path, 'r', encoding='utf-8') as f:
        try:
            prompts = json.load(f)
        except json.JSONDecodeError:
            print(f"错误: 配置文件 '{config_path}' 格式不正确。")
            sys.exit(1)

    if len(sys.argv) < 2:
        print_usage(prompts)
        return

    command = sys.argv[1]

    if command == 'list':
        print("可用的提示词命令:\n")
        for name, data in prompts.items():
            print(f"  {name}:\n    {data['description']}\n")
    elif command in prompts:
        template = prompts[command]['template']
        raw_args = sys.argv[2:]
        processed_args = []
        for arg in raw_args:
            if arg.startswith('@'):
                filepath = arg[1:]
                if os.path.exists(filepath):
                    with open(filepath, 'r', encoding='utf-8') as f:
                        processed_args.append(f.read())
                else:
                    print(f"错误: 作为参数提供的文件未找到: {filepath}")
                    sys.exit(1)
            else:
                processed_args.append(arg)
        
        try:
            formatted_prompt = template.format(*processed_args)
            print(formatted_prompt)
        except IndexError:
            print(f"错误: 参数数量不足。模板 '{command}' 需要的参数没有完全提供。")
            print(f"模板: {template}")
    else:
        print(f"错误: 未知的命令或提示词 '{command}'。")
        print_usage(prompts)

def print_usage(prompts):
    print("用法: prompt <命令> [参数...]")
    print("\n命令:")
    print("  list              列出所有可用的提示词。")
    print("  <提示词名称>    使用一个提示词模板。如果参数是多行文本或包含特殊字符，可将内容保存到文件，并使用 @filepath 作为参数。")
    if prompts:
        print("\n可用的提示词有:")
        for name in prompts.keys():
            print(f"    - {name}")

if __name__ == '__main__':
    main()
