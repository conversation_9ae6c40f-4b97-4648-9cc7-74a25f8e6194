
import csv
import re
import os

# --- Configuration ---
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_file_path = os.path.join(project_root, '03_reports', 'songs_sheet1_only.txt')
output_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report_cleaned_final.csv')

# Regex to find potential song numbers
SONG_ID_REGEX = r"['a-zA-Z0-9]{6,}"


def find_representative_song_id(row_data):
    if len(row_data) > 8:
        search_string = ' '.join(row_data[6:9])
        matches = re.findall(SONG_ID_REGEX, search_string)
        return matches[0] if matches else ''
    return ''

# --- Main Execution ---

processed_rows = []
song_count = 0

try:
    with open(input_file_path, 'r', encoding='utf-8') as f_in:
        # Use csv.reader directly on the file object. This is the robust way.
        reader = csv.reader(f_in)
        
        # Read header
        header = next(reader)
        header.append('统一歌号')
        processed_rows.append(header)
        
        # Process data rows
        for row in reader:
            if not row:  # Skip empty rows
                continue
            
            song_count += 1
            song_id = find_representative_song_id(row)
            row.append(song_id)
            
            # Pad/truncate row to match header length
            while len(row) < len(header):
                row.append('')
            processed_rows.append(row[:len(header)])

    with open(output_file_path, 'w', newline='', encoding='utf-8-sig') as f_out:
        writer = csv.writer(f_out)
        writer.writerows(processed_rows)

    # The header is not a song, so subtract 1 from the total rows processed.
    actual_song_count = len(processed_rows) - 1
    print(f"清理完成，共处理 {actual_song_count} 首歌曲。已将结果保存到: {os.path.abspath(output_file_path)}")

except Exception as e:
    print(f"处理时发生错误: {e}")

