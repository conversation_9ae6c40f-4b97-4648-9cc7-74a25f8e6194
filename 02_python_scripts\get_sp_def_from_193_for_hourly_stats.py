
import pyodbc
import os

# Connection details for the store server (193)
# Using the known credentials for the KTV project
SERVER = os.getenv('DB_HOST_193', '*************')
DATABASE = os.getenv('DB_NAME_193', 'dbfood') # Based on previous context, the SP is likely in dbfood
USERNAME = os.getenv('DB_USER_193', 'sa')
PASSWORD = os.getenv('DB_PASS_193', 'Musicbox@123')

# Stored procedure to retrieve
SP_NAME = 'usp_LogHourlyRoomStatistics'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;'

print(f"Connecting to {SERVER}/{DATABASE} to retrieve definition for SP: {SP_NAME}")

try:
    cnxn = pyodbc.connect(conn_str, timeout=5)
    cursor = cnxn.cursor()
    print("Connection successful.")

    # Using sp_helptext to get the definition
    sql = f"EXEC sp_helptext '{SP_NAME}'"
    print(f"Executing: {sql}")
    
    cursor.execute(sql)
    
    rows = cursor.fetchall()
    
    if not rows:
        print(f"Error: Stored procedure '{SP_NAME}' not found in database '{DATABASE}'.")
    else:
        print(f"--- Definition for {SP_NAME} ---")
        for row in rows:
            print(row[0], end='')
        print(f"\n--- End of definition ---")

except pyodbc.OperationalError as ex:
    sqlstate = ex.args[0]
    if 'HYT00' in sqlstate or '08001' in sqlstate:
        print(f"Error: Connection timed out or failed. Could not connect to server {SERVER}.")
        print("Please check network connectivity, firewall rules, and if the server is online.")
    else:
        print(f"An operational error occurred: {ex}")
except pyodbc.Error as ex:
    print(f"A database error occurred: {ex}")
except Exception as ex:
    print(f"An unexpected error occurred: {ex}")

finally:
    if 'cursor' in locals() and cursor:
        cursor.close()
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
    print("Connection closed.")
