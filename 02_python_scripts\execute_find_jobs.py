import pyodbc

# Database connection details - CORRECTED to connect to msdb
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=msdb;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\find_existing_jobs.sql'

cnxn = None
cursor = None
try:
    with open(sql_file_path, 'r', encoding='utf-8-sig') as f:
        sql_query = f.read()

    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    cursor.execute(sql_query)
    
    rows = cursor.fetchall()
    columns = [column[0] for column in cursor.description]

    if not rows:
        print("No enabled SQL Server Agent jobs found.")
    else:
        # Find max column widths for formatting
        widths = [len(col) for col in columns]
        for row in rows:
            for i, item in enumerate(row):
                widths[i] = max(widths[i], len(str(item)))

        # Print header
        header = ' | '.join(f'{col:<{widths[i]}}' for i, col in enumerate(columns))
        print(header)
        print('-' * len(header))

        # Print rows
        for row in rows:
            formatted_row = ' | '.join(f'{str(item):<{widths[i]}}' for i, item in enumerate(row))
            print(formatted_row)

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()