# This script checks for missing months in the openhistory_bak table for the year 2018.
# Version 3: All triple-quoted strings have been removed to prevent parsing errors.

import pyodbc

# Connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# SQL query as a single-line string to avoid quote conflicts.
SQL = "SELECT DISTINCT SUBSTRING(ComeDate, 6, 2) AS Month FROM cloudRms2019.rms2019.dbo.openhistory_bak WHERE ComeDate LIKE '2018-%%' AND ISDATE(ComeDate) = 1;"

def main():
    print("Connecting to the database...")
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        print("Executing query to find existing months in 2018 for openhistory_bak...")
        cursor.execute(SQL)
        
        rows = cursor.fetchall()
        existing_months = {row.Month for row in rows if row.Month and row.Month.isdigit()}
        
        print(f"Found months in the table: {sorted(list(existing_months))}")

        # Define all months
        all_months = {f"{i:02d}" for i in range(1, 13)}
        
        # Find the difference
        missing_months = sorted(list(all_months - existing_months))
        
        print("-"*50)
        if not missing_months:
            print("Result: For the year 2018, no months are missing from openhistory_bak.")
        else:
            print(f"Result: For the year 2018, the following months are missing: {missing_months}")
        print("-"*50)

    except pyodbc.Error as ex:
        print(f"Database query failed: {ex}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("\nConnection closed.")

if __name__ == "__main__":
    main()
