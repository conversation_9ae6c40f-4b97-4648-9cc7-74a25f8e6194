ALTER PROCEDURE dbo.usp_Job_Run_Daily_ETL
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @TargetDate DATE = DATEADD(DAY, -1, GETDATE());

    PRINT N'--- 开始执行每日ETL总调度任务，目标日期: ' + CONVERT(NVARCHAR, @TargetDate, 120) + N' ---';

    -- 任务1: 更新批次规模汇总表
    BEGIN TRY
        PRINT N'
[任务 1/3] 正在执行 usp_Populate_Fact_Daily_BatchSize_Summary...';
        EXEC dbo.usp_Populate_Fact_Daily_BatchSize_Summary @RunDate = @TargetDate;
        PRINT N' -> usp_Populate_Fact_Daily_BatchSize_Summary 执行成功。';
    END TRY
    BEGIN CATCH
        PRINT N' -> 错误: 执行 usp_Populate_Fact_Daily_BatchSize_Summary 失败。' + ERROR_MESSAGE();
    END CATCH

    -- 任务2: 循环更新每日门店汇总表
    PRINT N'
[任务 2/3] 正在循环执行 usp_Populate_Fact_Daily_Shop_Summary...';
    DECLARE @ShopId INT;
    DECLARE @ShopName NVARCHAR(100);

    DECLARE ShopCursor CURSOR FOR
        SELECT ShopID, ShopName FROM dbo.Dim_Shop WHERE IsActive = 1 ORDER BY ShopID;

    OPEN ShopCursor;
    FETCH NEXT FROM ShopCursor INTO @ShopId, @ShopName;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            PRINT N'  -> 正在处理门店: ' + CAST(@ShopId AS NVARCHAR(10)) + N' (' + @ShopName + N')';
            EXEC dbo.usp_Populate_Fact_Daily_Shop_Summary @TargetDate = @TargetDate, @ShopId = @ShopId;
        END TRY
        BEGIN CATCH
            PRINT N'  -> 错误: 处理门店 ' + CAST(@ShopId AS NVARCHAR(10)) + N' 失败。' + ERROR_MESSAGE();
        END CATCH
        FETCH NEXT FROM ShopCursor INTO @ShopId, @ShopName;
    END

    CLOSE ShopCursor;
    DEALLOCATE ShopCursor;

    PRINT N' -> 所有门店处理完毕。';

    -- 新增任务3: 如果是每月的第一天，则执行月度汇总
    IF DAY(GETDATE()) = 1
    BEGIN
        PRINT N'
[任务 3/3] 检测到是本月第一天，开始执行月度汇总任务...';
        DECLARE @TargetYear INT = YEAR(@TargetDate);
        DECLARE @TargetMonth INT = MONTH(@TargetDate);
        
        BEGIN TRY
            PRINT N'  -> 正在为 ' + CAST(@TargetYear AS NVARCHAR(4)) + N'- ' + CAST(@TargetMonth AS NVARCHAR(2)) + N' 执行 usp_Populate_Fact_Monthly_Summary...';
            EXEC dbo.usp_Populate_Fact_Monthly_Summary @TargetYear = @TargetYear, @TargetMonth = @TargetMonth;
            PRINT N'  -> 月度汇总任务执行成功。';
        END TRY
        BEGIN CATCH
            PRINT N'  -> 错误: 执行月度汇总任务失败。' + ERROR_MESSAGE();
        END CATCH
    END
    ELSE
    BEGIN
        PRINT N'
[任务 3/3] 今天不是本月第一天，跳过月度汇总任务。';
    END

    PRINT N'
--- 每日ETL总调度任务执行完毕 ---';
    SET NOCOUNT OFF;
END
