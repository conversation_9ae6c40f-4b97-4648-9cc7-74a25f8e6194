
import pyodbc
import pandas as pd

# --- Configuration ---
DB_SERVER = '192.168.2.5'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
OUTPUT_FILE = 'C:/Users/<USER>/CascadeProjects/KTV_Data_Analysis/03_reports/night_details_fix_backup.md'

def backup_problematic_data():
    """Fetches records with NonPackage_RoomFee <= 0 and saves them to a markdown file."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
    
    sql = """
    SELECT 
        h.ReportID,
        h.ShopID,
        h.ReportDate,
        nd.NonPackage_RoomFee AS Current_NonPackage_RoomFee,
        nd.Night_Verify_BatchCount,
        nd.FreeMeal_BatchCount,
        nd.Buyout_BatchCount,
        nd.<PERSON>yi<PERSON>_BatchCount,
        nd.FreeConsumption_BatchCount,
        nd.NonPackage_Special,
        nd.NonPackage_Meituan,
        nd.NonPackage_Douyin,
        nd.NonPackage_Others
    FROM 
        dbo.FullDailyReport_NightDetails nd
    JOIN 
        dbo.FullDailyReport_Header h ON nd.ReportID = h.ReportID
    WHERE 
        nd.NonPackage_RoomFee <= 0
    ORDER BY
        h.ReportDate DESC, h.ShopID;
    """

    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        print("数据库连接成功，正在查询有问题的数据...")
        df = pd.read_sql(sql, cnxn)
        print(f"查询到 {len(df)} 条需要备份的记录。")

        if not df.empty:
            md_content = "# NonPackage_RoomFee 修复前数据备份\n\n"
            md_content += f"备份时间: {pd.Timestamp.now()}\n\n"
            md_content += "以下是 `FullDailyReport_NightDetails` 表中 `NonPackage_RoomFee <= 0` 的所有记录的当前值。\n\n"
            md_content += df.to_markdown(index=False)

            with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                f.write(md_content)
            print(f"备份文件已成功保存到: {OUTPUT_FILE}")
        else:
            print("未找到需要备份的数据。")

    except Exception as e:
        print(f"执行出错: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    backup_problematic_data()
