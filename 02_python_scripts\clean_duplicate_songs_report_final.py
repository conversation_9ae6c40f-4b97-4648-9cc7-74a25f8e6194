
import csv
import re
import os

# --- Configuration ---
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_file_path = os.path.join(project_root, '03_reports', 'songs_sheet1_only.txt')
output_file_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report_cleaned_final.csv')

# Regex to find potential song numbers
SONG_ID_REGEX = r"['a-zA-Z0-9]{6,}"


def find_representative_song_id(row_data):
    # Combine the relevant columns into a single string for searching
    # Columns are: 备注 (6), 相同 (7), 不相同 (8)
    if len(row_data) > 8:
        search_string = ' '.join(row_data[6:9])
        matches = re.findall(SONG_ID_REGEX, search_string)
        return matches[0] if matches else ''
    return ''

# --- Main Execution ---

# Find the start of the actual CSV data
all_lines = []
with open(input_file_path, 'r', encoding='utf-8') as f_in:
    all_lines = f_in.readlines()

csv_data_lines = []
header_found = False
for line in all_lines:
    stripped_line = line.strip()
    if not header_found and stripped_line and '歌曲名称' in stripped_line:
        header_found = True
        csv_data_lines.append(line)
    elif header_found and not stripped_line.startswith('--- Sheet:'):
        csv_data_lines.append(line)

processed_rows = []
song_count = 0

if csv_data_lines:
    # Use the csv module to correctly handle multiline fields
    csv_reader = csv.reader(csv_data_lines)
    
    # First row is the header
    header = next(csv_reader)
    header.append('统一歌号')
    processed_rows.append(header)
    
    # Process data rows
    for row in csv_reader:
        if not row: # Skip any empty rows that might result
            continue

        song_count += 1
        song_id = find_representative_song_id(row)
        
        # Append the new song ID
        row.append(song_id)
        
        # Ensure row length matches header length
        while len(row) < len(header):
            row.append('')
        processed_rows.append(row[:len(header)])

# Write the cleaned data to a new CSV file
if processed_rows:
    with open(output_file_path, 'w', newline='', encoding='utf-8-sig') as f_out:
        writer = csv.writer(f_out)
        writer.writerows(processed_rows)
    print(f"清理完成，共处理 {song_count} 首歌曲。已将结果保存到: {os.path.abspath(output_file_path)}")
else:
    print("没有数据被处理。")

