import pyodbc
from collections import Counter

# Connection details
conn_str = """
    DRIVER={ODBC Driver 17 for SQL Server};
    SERVER=192.168.2.5;
    DATABASE=operatedata;
    UID=sa;
    PWD=Musicbox123;
    TrustServerCertificate=yes;
"""

# SQL query to get only the channel for analysis
sql_query = "SELECT Channel FROM Dim_Deal_Item_Mapping;"

try:
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    print("数据库连接成功，正在查询并分析映射表内容...")
    
    cursor.execute(sql_query)
    # Fetch all channel names
    channels = [row.Channel for row in cursor.fetchall()]
    
    if not channels:
        print("映射表中没有数据。")
    else:
        # Count occurrences of each channel
        channel_counts = Counter(channels)
        
        print("\n--- 映射表分类结果汇总 ---")
        for channel, count in channel_counts.items():
            print(f"渠道: {channel}, 记录数: {count}")
        print("--------------------------")
        print(f"总计: {len(channels)} 条记录")

except Exception as e:
    print(f"操作出错: {e}")
finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("\n数据库连接已关闭。")
