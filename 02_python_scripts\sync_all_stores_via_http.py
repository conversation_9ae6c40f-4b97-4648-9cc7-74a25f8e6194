# -*- coding: utf-8 -*-
"""
KTV门店数据同步脚本 (HTTP-API版)

功能:
通过调用HTTP接口，自动同步所有指定门店的 Food 和 FoodLabel 数据。

如何使用:
直接在命令行中运行此脚本: python sync_all_stores_via_http.py
"""

import urllib.request
import urllib.error
import socket

# --- 配置区域 ---

# 在这里修改您需要同步的门店ID列表
# 例如，如果您有1、2、3、5、8号门店，可以写成: SHOP_IDS = [1, 2, 3, 5, 8]
SHOP_IDS = [2, 3, 4, 5, 6, 8, 9, 10, 11]  # 已更新为指定的门店ID列表

# 数据同步接口的URL模板。
# {shop_id} 将被替换为实际的门店ID。
BASE_URL = "http://ktv{shop_id}.tang-hui.com.cn:90/fsms/DataPull_Public.ashx"

# 需要同步的数据端点。
# 键是方便我们阅读的名称，值是URL的查询参数。
ENDPOINTS_TO_SYNC = {
    "商品主数据(Food)": "In=OperateData.[dbo].[food]&Ex=date_food",
    "商品标签(FoodLabel)": "In=OperateData.[dbo].[foodlabel]&Ex=date_foodlabel"
}

# 设置每个HTTP请求的超时时间（秒）
REQUEST_TIMEOUT = 20

# --- 脚本主逻辑 ---

def trigger_sync_for_store(shop_id):
    """
    为单个门店触发所有数据端点的同步。
    """
    print(f"--- 开始同步门店 ShopId: {shop_id} ---")

    for endpoint_name, params in ENDPOINTS_TO_SYNC.items():
        # 格式化最终的请求URL
        url = f"{BASE_URL.format(shop_id=shop_id)}?{params}"
        
        print(f"  -> 正在调用 [{endpoint_name}] 接口...")
        print(f"     URL: {url}")

        try:
            # 发起HTTP GET请求，并设置超时
            with urllib.request.urlopen(url, timeout=REQUEST_TIMEOUT) as response:
                # 检查HTTP响应状态码
                if response.status == 200:
                    print(f"  <- [成功] {endpoint_name} 同步已成功触发。")
                else:
                    print(f"  <- [警告] {endpoint_name} 接口返回状态码: {response.status}。")
        
        # 异常处理
        except socket.timeout:
            print(f"  <- [失败] {endpoint_name} 请求超时（超过 {REQUEST_TIMEOUT} 秒）。门店服务器可能无响应。")
        except urllib.error.URLError as e:
            # 处理URL相关的错误，例如无法解析域名、网络连接失败等
            print(f"  <- [失败] {endpoint_name} 请求失败。原因: {e.reason}")
        except Exception as e:
            # 处理其他所有意想不到的错误
            print(f"  <- [失败] {endpoint_name} 发生未知错误: {e}")

    print(f"--- 门店 ShopId: {shop_id} 同步完成 ---
")


# --- 程序入口 ---
if __name__ == "__main__":
    print("=============================================")
    print("     开始执行所有门店数据同步任务")
    print("=============================================\n")

    for store_id in SHOP_IDS:
        trigger_sync_for_store(store_id)

    print("=============================================")
    print("     所有门店数据同步任务已执行完毕")
    print("=============================================")
