
ALTER PROCEDURE dbo.usp_Refresh_Dim_Deal_Map
AS
BEGIN
    SET NOCOUNT ON;

    PRINT 'Starting refresh of Dim_Deal_Map...';

    TRUNCATE TABLE Dim_Deal_Map;
    PRINT 'Dim_Deal_Map table has been truncated.';

    -- CTE to assign a specific SubChannelName based on deal name
    WITH FoodWithSubChannel AS (
        SELECT
            ShopId,
            FdNo,
            FdCName,
            FdPrice2,
            CASE
                -- Bank channels
                WHEN FdCName LIKE N'%广发%' THEN N'广发银行'
                WHEN FdCName LIKE N'%中信%' THEN N'中信银行'
                WHEN FdCName LIKE N'%广日%' THEN N'广日银联'
                -- Meituan channels (specific first)
                WHEN FdCName LIKE N'%美预%' THEN N'美预'
                WHEN FdCName LIKE N'%美团%' THEN N'美团'
                -- Douyin channels (specific first)
                WHEN FdCName LIKE N'%抖预%' THEN N'抖预'
                WHEN FdCName LIKE N'%抖音%' THEN N'抖音'
                ELSE NULL
            END AS SubChannelName_Derived
        FROM food
        WHERE FdType = 'T' -- Process only deal-type items
    )
    INSERT INTO Dim_Deal_Map (ShopId, FdNo, ChannelSK, Source_FdName, FdPrice2, CreateTime, UpdateTime)
    SELECT
        fwc.ShopId,
        fwc.FdNo,
        dc.ChannelSK,
        fwc.FdCName,
        fwc.FdPrice2,
        GETDATE(),
        GETDATE()
    FROM FoodWithSubChannel fwc
    -- Join on SubChannelName for precise mapping
    JOIN Dim_Channel dc ON fwc.SubChannelName_Derived = dc.SubChannelName
    WHERE fwc.SubChannelName_Derived IS NOT NULL;

    PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows have been inserted into Dim_Deal_Map.';
    PRINT 'Refresh of Dim_Deal_Map completed successfully.';

END;
GO
