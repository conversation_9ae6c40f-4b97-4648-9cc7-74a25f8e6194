
ALTER PROCEDURE dbo.usp_Refresh_Dim_Deal_Map
AS
BEGIN
    SET NOCOUNT ON;

    PRINT 'Starting refresh of Dim_Deal_Map from the correct source: operatedata.dbo.food';

    TRUNCATE TABLE Dim_Deal_Map;
    PRINT 'Dim_Deal_Map table has been truncated.';

    -- CTE to derive channel information from the food table
    WITH DealsWithSubChannel AS (
        SELECT
            f.ShopId,
            f.FdNo,
            f.FdCName,
            f.FdPrice2,
            CASE
                WHEN f.FdCName LIKE N'%广发%' THEN N'广发银行'
                WHEN f.FdCName LIKE N'%中信%' THEN N'中信银行'
                WHEN f.FdCName LIKE N'%广日%' THEN N'广日银联'
                WHEN f.FdCName LIKE N'%美预%' THEN N'美预'
                WHEN f.FdCName LIKE N'%美团%' THEN N'美团'
                WHEN f.FdCName LIKE N'%抖预%' THEN N'抖预'
                WHEN f.FdCName LIKE N'%抖音%' THEN N'抖音'
                ELSE NULL
            END AS SubChannelName_Derived
        FROM operatedata.dbo.food AS f
    )
    -- Insert the categorized and mapped data into the final table
    INSERT INTO Dim_Deal_Map (ShopId, FdNo, ChannelSK, Source_FdName, FdPrice2, CreateTime, UpdateTime)
    SELECT
        dws.ShopId,
        dws.FdNo,
        dc.ChannelSK,
        dws.FdCName,
        dws.FdPrice2,
        GETDATE(),
        GETDATE()
    FROM DealsWithSubChannel dws
    JOIN Dim_Channel dc ON dws.SubChannelName_Derived = dc.SubChannelName
    WHERE dws.SubChannelName_Derived IS NOT NULL;

    PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows have been inserted into Dim_Deal_Map.';
    PRINT 'Refresh of Dim_Deal_Map completed successfully.';

END;
GO
