
import pyodbc

# --- Configuration ---
DB_SERVER = '192.168.2.5'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
SP_NAME = 'usp_Util_GetTimeSlotDetailsWithDirectFall'

def get_sp_definition():
    """Connects to the database and retrieves the definition of a stored procedure."""
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
    
    cnxn = None
    try:
        cnxn = pyodbc.connect(conn_str)
        cursor = cnxn.cursor()
        print(f"数据库连接成功，正在获取存储过程 '{SP_NAME}' 的定义...\n")
        
        # Query to get the stored procedure definition
        sql = """
        SELECT OBJECT_DEFINITION(OBJECT_ID(?))
        """
        
        cursor.execute(sql, SP_NAME)
        row = cursor.fetchone()
        
        if row and row[0]:
            print(f"--- 存储过程: {SP_NAME} ---")
            print(row[0])
            print("\n--- 定义结束 ---")
        else:
            print(f"错误: 无法找到名为 '{SP_NAME}' 的存储过程。")

    except pyodbc.Error as ex:
        print(f"数据库操作出错: {ex}")
    finally:
        if cnxn:
            cnxn.close()
            print("\n数据库连接已关闭。")

if __name__ == '__main__':
    get_sp_definition()
