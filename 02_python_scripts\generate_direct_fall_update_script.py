import pyodbc
import pandas as pd

# --- Configuration ---
DB_SERVER = '***********'
DB_DATABASE = 'operatedata'
DB_USERNAME = 'sa'
DB_PASSWORD = 'Musicbox123'
OUTPUT_SQL_FILE = 'C:/Users/<USER>/CascadeProjects/KTV_Data_Analysis/01_sql_scripts/maintenance/generated_direct_fall_bulk_update.sql'
SHOP_ID = 3

def generate_hardcoded_update_script():
    """
    Generates a bulk SQL UPDATE script with hardcoded values to avoid execution environment issues.
    """
    print("Generating hardcoded UPDATE script...")
    cnxn = None
    try:
        # 1. Connect to the database
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_DATABASE};UID={DB_USERNAME};PWD={DB_PASSWORD};TrustServerCertificate=yes;'
        cnxn = pyodbc.connect(conn_str)
        print("Database connection successful.")

        # 2. Execute the optimized query to get the correct counts
        calculation_sql = f"""
        WITH MinCashTimeCTE AS (
            SELECT InvNo, MIN(CashTime) AS MinCashTime
            FROM operatedata.dbo.FdCashBak
            WHERE ShopId = {SHOP_ID}
            GROUP BY InvNo
        ),
        DirectFallInvoices AS (
            SELECT r.WorkDate, r.OpenDateTime, r.Beg_Key, mct.MinCashTime
            FROM dbo.RmCloseInfo r
            LEFT JOIN MinCashTimeCTE mct ON r.InvNo = mct.InvNo COLLATE Chinese_PRC_CI_AS
            WHERE r.IsDirectFall = 1
              AND r.ShopId = {SHOP_ID}
              AND r.WorkDate BETWEEN '20250801' AND '20250831'
        ),
        ClassifiedInvoices AS (
            SELECT
                dfi.WorkDate,
                CASE
                    WHEN dfi.OpenDateTime IS NOT NULL THEN 
                        CASE WHEN DATEPART(hour, dfi.OpenDateTime) < 20 THEN 'Day' ELSE 'Night' END
                    WHEN dfi.MinCashTime IS NOT NULL THEN
                        CASE WHEN CAST(LEFT(dfi.MinCashTime, 2) AS INT) < 20 THEN 'Day' ELSE 'Night' END
                    WHEN dfi.Beg_Key IS NOT NULL THEN
                        (SELECT TOP 1 CASE WHEN sti.TimeMode = 1 THEN 'Day' ELSE 'Night' END FROM dbo.shoptimeinfo sti WHERE sti.Shopid = {SHOP_ID} AND sti.TimeNo = dfi.Beg_Key)
                    ELSE 'Night'
                END AS Slot_Classification
            FROM DirectFallInvoices dfi
        )
        SELECT 
            WorkDate,
            COUNT(CASE WHEN Slot_Classification = 'Day' THEN 1 END) AS DayTimeDropInBatch,
            COUNT(CASE WHEN Slot_Classification = 'Night' THEN 1 END) AS NightTimeDropInBatch
        FROM ClassifiedInvoices
        GROUP BY WorkDate
        ORDER BY WorkDate;
        """
        print("Executing optimized query to calculate correct values...")
        correct_counts_df = pd.read_sql(calculation_sql, cnxn)
        print(f"Calculation complete, found {len(correct_counts_df)} days of data.")

        # 3. Generate hardcoded UPDATE statements
        all_update_statements = [
            "USE operatedata;",
            "GO",
            f"-- Bulk update for Direct Fall counts for ShopID={SHOP_ID} for August 2025",
            "PRINT 'Starting hardcoded bulk update...';",
            "GO"
        ]

        for index, row in correct_counts_df.iterrows():
            work_date = row['WorkDate']
            day_count = row['DayTimeDropInBatch']
            night_count = row['NightTimeDropInBatch']
            
            update_sql = (
                f"UPDATE dbo.FullDailyReport_Header "
                f"SET DayTimeDropInBatch = {day_count}, NightTimeDropInBatch = {night_count} "
                f"WHERE ReportDate = '{work_date}' AND ShopID = {SHOP_ID};"
            )
            all_update_statements.append(update_sql)

        all_update_statements.append("PRINT 'Bulk update complete.';")
        all_update_statements.append("GO")

        # 4. Write to file
        with open(OUTPUT_SQL_FILE, 'w', encoding='utf-8') as f:
            f.write('\n'.join(all_update_statements))
        
        print(f"\nSuccess! Script with {len(correct_counts_df)} UPDATE statements has been generated.")
        print(f"File path: {OUTPUT_SQL_FILE}")

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        if cnxn:
            cnxn.close()
            print("Database connection closed.")

if __name__ == '__main__':
    generate_hardcoded_update_script()