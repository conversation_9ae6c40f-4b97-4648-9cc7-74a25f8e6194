
ALTER PROCEDURE dbo.usp_Refresh_Dim_Deal_Map
AS
BEGIN
    SET NOCOUNT ON;

    PRINT 'Starting refresh of Dim_Deal_Map...';

    -- Use a temporary table to hold the deduplicated source data
    CREATE TABLE #SourceDeals (
        ShopId INT,
        FdNo VARCHAR(5),
        FdCName NVARCHAR(100),
        FdPrice INT
    );

    -- Insert distinct deals from the actual transaction table
    INSERT INTO #SourceDeals (ShopId, FdNo, FdCName, FdPrice)
    SELECT DISTINCT
        Shopid,
        FdNo,
        FdCName,
        FdPrice
    FROM operatedata.dbo.FdCashBak
    WHERE ISNULL(FdNo, '') <> ''; -- Ensure FdNo is not null or empty

    PRINT 'Truncating Dim_Deal_Map...';
    TRUNCATE TABLE Dim_Deal_Map;

    -- CTE to assign a specific SubChannelName based on deal name from the temp table
    WITH DealsWithSubChannel AS (
        SELECT
            ShopId,
            FdNo,
            FdCName,
            FdPrice,
            CASE
                WHEN FdCName LIKE N'%广发%' THEN N'广发银行'
                WHEN FdCName LIKE N'%中信%' THEN N'中信银行'
                WHEN FdCName LIKE N'%广日%' THEN N'广日银联'
                WHEN FdCName LIKE N'%美预%' THEN N'美预'
                WHEN FdCName LIKE N'%美团%' THEN N'美团'
                WHEN FdCName LIKE N'%抖预%' THEN N'抖预'
                WHEN FdCName LIKE N'%抖音%' THEN N'抖音'
                ELSE NULL
            END AS SubChannelName_Derived
        FROM #SourceDeals
    )
    INSERT INTO Dim_Deal_Map (ShopId, FdNo, ChannelSK, Source_FdName, FdPrice2, CreateTime, UpdateTime)
    SELECT
        dws.ShopId,
        dws.FdNo,
        dc.ChannelSK,
        dws.FdCName,
        dws.FdPrice, -- Using the correct FdPrice column
        GETDATE(),
        GETDATE()
    FROM DealsWithSubChannel dws
    JOIN Dim_Channel dc ON dws.SubChannelName_Derived = dc.SubChannelName
    WHERE dws.SubChannelName_Derived IS NOT NULL;

    PRINT CAST(@@ROWCOUNT AS VARCHAR) + ' rows have been inserted into Dim_Deal_Map.';

    -- Clean up the temporary table
    DROP TABLE #SourceDeals;

    PRINT 'Refresh of Dim_Deal_Map completed successfully.';

END;
GO
