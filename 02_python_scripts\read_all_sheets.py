import pandas as pd
import os

# 文件路径
file_name = '储值套餐统计表.xlsx'
file_path = os.path.join(os.getcwd(), file_name)

def read_excel_report():
    """
    读取Excel文件中的所有工作表并打印内容
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：在项目根目录下找不到文件 '{file_name}'")
            print(f"完整路径: {file_path}")
            return

        # 读取Excel文件的所有工作表
        print(f"正在读取文件: {file_path}")
        excel_file = pd.ExcelFile(file_path, engine='openpyxl')
        print(f"工作表名称: {excel_file.sheet_names}")
        
        # 读取每个工作表的内容
        for i, sheet_name in enumerate(excel_file.sheet_names):
            print(f"\n--- 工作表 {i+1}: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            print(f"行列数: {df.shape}")
            print("前20行数据:")
            print(df.head(20))
            print("\n列名:")
            print(list(df.columns))
            print("\n数据类型:")
            print(df.dtypes)
            
    except Exception as e:
        print(f"读取或处理文件时发生错误: {e}")

if __name__ == '__main__':
    read_excel_report()