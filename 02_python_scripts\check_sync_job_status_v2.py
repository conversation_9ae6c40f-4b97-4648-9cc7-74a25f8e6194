
'''
This script checks the status of SQL Server Agent Jobs related to data sync on the *********** server.
It queries the msdb database to find jobs like 'RMS_Daily_Data_Sync' or 'RMS_Daily_Data_Sync_to_HQ',
and checks their enabled status and last run time.
'''
import pyodbc
import pandas as pd

# Connection details
server = '***********'
database = 'msdb'  # System database for SQL Agent information
username = 'sa'
password = 'Musicbox123'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'

# SQL query to get job information
# We join sysjobs with sysjobhistory to get the last run status.
sql = """
WITH JobHistory AS (
    SELECT
        job_id,
        run_date,
        run_time,
        run_status,
        ROW_NUMBER() OVER(PARTITION BY job_id ORDER BY run_date DESC, run_time DESC) as rn
    FROM msdb.dbo.sysjobhistory
    WHERE step_id = 0 -- Job outcome step
)
SELECT 
    j.name AS job_name,
    j.enabled AS is_enabled,
    CASE jh.run_status
        WHEN 0 THEN 'Failed'
        WHEN 1 THEN 'Succeeded'
        WHEN 2 THEN 'Retry'
        WHEN 3 THEN 'Canceled'
        WHEN 4 THEN 'In progress'
        ELSE 'Unknown'
    END AS last_run_status,
    msdb.dbo.agent_datetime(jh.run_date, jh.run_time) AS last_run_datetime
FROM msdb.dbo.sysjobs j
LEFT JOIN JobHistory jh ON j.job_id = jh.job_id AND jh.rn = 1
WHERE j.name LIKE N'%RMS_Daily_Data_Sync%'
ORDER BY j.name;
"""

try:
    # Connect to the database
    cnxn = pyodbc.connect(conn_str)
    print(f"Successfully connected to {server}/{database}")

    # Execute the query
    df = pd.read_sql(sql, cnxn)

    if df.empty:
        print("\n--- Job Status ---")
        print("No jobs found with names like '%RMS_Daily_Data_Sync%'.")
        print("This is the likely cause of the problem.")
        print("--- End of Status ---")
    else:
        print("\n--- Job Status ---")
        for index, row in df.iterrows():
            print(f"Job Name:          {row['job_name']}")
            print(f"Is Enabled:        { 'Yes' if row['is_enabled'] == 1 else 'No' }")
            print(f"Last Run Status:   {row['last_run_status']}")
            # Format the datetime for better readability
            last_run = row['last_run_datetime'].strftime('%Y-%m-%d %H:%M:%S') if pd.notna(row['last_run_datetime']) else 'Never'
            print(f"Last Run Time:     {last_run}")
            print("---")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database connection or query failed.")
    print(f"SQLSTATE: {sqlstate}")
    print(ex)

finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("Connection closed.")
