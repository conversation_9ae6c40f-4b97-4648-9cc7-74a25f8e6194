import pyodbc
import pandas as pd
from datetime import datetime

def execute_batchsize_summary():
    """
    执行存储过程获取2025-07-01的批次大小汇总数据
    """
    # 数据库连接配置
    server = '192.168.2.5'
    database = 'operatedata'
    username = 'your_username'  # 替换为实际用户名
    password = 'your_password'  # 替换为实际密码
    
    try:
        # 建立数据库连接
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # 执行存储过程
        run_date = '2025-07-01'
        print(f"正在执行存储过程，日期: {run_date}")
        cursor.execute("EXEC usp_Populate_Fact_Daily_BatchSize_Summary ?", run_date)
        
        # 提交事务
        conn.commit()
        print("存储过程执行完成")
        
        # 查询结果
        query = """
        SELECT * FROM dbo.Fact_Daily_BatchSize_Summary
        WHERE DateSK = (SELECT DateSK FROM dbo.Dim_Date WHERE FullDate = ?)
        """
        df = pd.read_sql(query, conn, params=[run_date])
        
        # 保存结果到CSV文件
        output_file = f'batchsize_summary_{run_date.replace("-", "")}.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_file}")
        print(f"共导出 {len(df)} 行数据")
        
        # 显示前几行数据
        print("\n前5行数据预览:")
        print(df.head())
        
        # 关闭连接
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"执行过程中出现错误: {str(e)}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    execute_batchsize_summary()