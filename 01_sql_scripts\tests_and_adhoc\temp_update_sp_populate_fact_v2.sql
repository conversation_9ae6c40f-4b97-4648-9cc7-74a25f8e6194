ALTER PROCEDURE dbo.usp_Populate_Fact_Daily_Shop_Summary
    @TargetDate DATE,
    @ShopId INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @DateSK INT, @ShopSK INT;
    SELECT @DateSK = DateSK FROM dbo.Dim_Date WHERE FullDate = @TargetDate;
    SELECT @ShopSK = ShopSK FROM dbo.Dim_Shop WHERE ShopID = @ShopId;

    IF @DateSK IS NULL OR @ShopSK IS NULL
    BEGIN
        PRINT N'错误: 无法在 Dim_Date 或 Dim_Shop 中找到对应的SK值。请检查日期和门店ID。';
        RETURN -1;
    END

    DELETE FROM dbo.stg_Fact_Daily_Shop_Summary WHERE DateSK = @DateSK AND ShopSK = @ShopSK;
    INSERT INTO dbo.stg_Fact_Daily_Shop_Summary (DateSK, ShopSK, ShopID, CreateTime, UpdateTime)
    VALUES (@DateSK, @ShopSK, @ShopId, GETDATE(), GETDATE());

    BEGIN TRY
        EXEC dbo.usp_Calculate_DayTimeMetrics_ForStaging @ShopId = @ShopId, @TargetDate = @TargetDate, @ShopSK = @ShopSK, @DateSK = @DateSK;
        EXEC dbo.usp_Calculate_Recharge_ForStaging @ShopId = @ShopId, @TargetDate = @TargetDate, @ShopSK = @ShopSK, @DateSK = @DateSK;
    END TRY
    BEGIN CATCH
        PRINT N'错误: 在暂存表计算阶段失败。' + ERROR_MESSAGE();
        DELETE FROM dbo.stg_Fact_Daily_Shop_Summary WHERE DateSK = @DateSK AND ShopSK = @ShopSK;
        RETURN -2;
    END CATCH

    MERGE dbo.Fact_Daily_Shop_Summary AS Target
    USING dbo.stg_Fact_Daily_Shop_Summary AS Source
    ON (Target.DateSK = Source.DateSK AND Target.ShopSK = Source.ShopSK)
    WHEN MATCHED THEN
        UPDATE SET
            Target.DayTimeRevenue = Source.DayTimeRevenue, Target.NightTimeRevenue = Source.NightTimeRevenue, Target.TotalRevenue = Source.TotalRevenue,
            Target.TotalBatches = Source.TotalBatches, Target.BuffetGuestCount = Source.BuffetGuestCount, Target.TotalDirectFallGuests = Source.TotalDirectFallGuests,
            Target.DayTimeBatchCount = Source.DayTimeBatchCount, Target.NightTimeBatchCount = Source.NightTimeBatchCount, Target.NightTimeGuestCount = Source.NightTimeGuestCount,
            Target.AvgPricePerBill_Buffet = Source.AvgPricePerBill_Buffet, Target.AvgPricePerGuest_Buffet = Source.AvgPricePerGuest_Buffet, Target.AvgPricePerGuest_PrimeTime = Source.AvgPricePerGuest_PrimeTime,
            
            -- Complimentary
            Target.ComplimentaryBatches = Source.ComplimentaryBatches,
            Target.ComplimentaryRevenue = Source.ComplimentaryRevenue,

            -- Privilege Booking
            Target.PrivilegeBooking_Count_0Yuan = Source.PrivilegeBooking_Count_0Yuan, Target.PrivilegeBooking_Count_5Yuan = Source.PrivilegeBooking_Count_5Yuan,
            Target.PrivilegeBooking_Count_10Yuan = Source.PrivilegeBooking_Count_10Yuan, Target.PrivilegeBooking_Count_15Yuan = Source.PrivilegeBooking_Count_15Yuan,
            Target.Revenue_PrivilegeBooking = Source.Revenue_PrivilegeBooking,

            -- Night-time Breakdown
            Target.Night_FreeMeal_Batches = Source.Night_FreeMeal_Batches, Target.Night_FreeMeal_Revenue = Source.Night_FreeMeal_Revenue,
            Target.Night_Buyout_Batches = Source.Night_Buyout_Batches, Target.Night_DrinkPackage_Batches = Source.Night_DrinkPackage_Batches,
            Target.Night_FreeConsumption_Batches = Source.Night_FreeConsumption_Batches, Target.Night_RoomFee_Batches = Source.Night_RoomFee_Batches,
            Target.Night_Other_Batches = Source.Night_Other_Batches,

            -- Channel Revenues & Fees
            Target.Revenue_OfficialAccount = Source.Revenue_OfficialAccount,
            Target.Revenue_Meituan = Source.Revenue_Meituan, Target.Revenue_Douyin = Source.Revenue_Douyin, Target.Revenue_Bank = Source.Revenue_Bank,
            Target.Revenue_Meituan_Booking = Source.Revenue_Meituan_Booking, Target.Revenue_Meituan_GroupBuy = Source.Revenue_Meituan_GroupBuy,
            Target.Revenue_Douyin_Booking = Source.Revenue_Douyin_Booking, Target.Revenue_Douyin_GroupBuy = Source.Revenue_Douyin_GroupBuy,
            Target.Revenue_Bank_GF = Source.Revenue_Bank_GF, Target.Revenue_Bank_CITIC = Source.Revenue_Bank_CITIC, Target.Revenue_Bank_UnionPay = Source.Revenue_Bank_UnionPay,
            Target.Fee_Meituan_Booking = Source.Fee_Meituan_Booking, Target.Fee_Meituan_GroupBuy = Source.Fee_Meituan_GroupBuy,
            Target.Fee_Douyin_Booking = Source.Fee_Douyin_Booking, Target.Fee_Douyin_GroupBuy = Source.Fee_Douyin_GroupBuy,
            Target.Fee_Bank_GF = Source.Fee_Bank_GF, Target.Fee_Bank_CITIC = Source.Fee_Bank_CITIC, Target.Fee_Bank_UnionPay = Source.Fee_Bank_UnionPay,
            Target.Fee_Bank_Total = Source.Fee_Bank_Total,

            -- Recharge Data
            Target.DailyRecharge_Total = Source.DailyRecharge_Total,
            Target.DailyRecharge_Meituan = Source.DailyRecharge_Meituan,
            Target.DailyRecharge_Meituan_Ratio = Source.DailyRecharge_Meituan_Ratio,
            Target.RechargeTier1_BatchCount = Source.RechargeTier1_BatchCount, Target.RechargeTier1_TotalAmount = Source.RechargeTier1_TotalAmount,
            Target.RechargeTier2_BatchCount = Source.RechargeTier2_BatchCount, Target.RechargeTier2_TotalAmount = Source.RechargeTier2_TotalAmount,
            Target.RechargeTier3_BatchCount = Source.RechargeTier3_BatchCount, Target.RechargeTier3_TotalAmount = Source.RechargeTier3_TotalAmount,
            Target.RechargeTier4_BatchCount = Source.RechargeTier4_BatchCount, Target.RechargeTier4_TotalAmount = Source.RechargeTier4_TotalAmount,
            Target.RechargeTier5_BatchCount = Source.RechargeTier5_BatchCount, Target.RechargeTier5_TotalAmount = Source.RechargeTier5_TotalAmount,
            Target.RechargeTier6_BatchCount = Source.RechargeTier6_BatchCount, Target.RechargeTier6_TotalAmount = Source.RechargeTier6_TotalAmount,
            Target.RechargeTier7_BatchCount = Source.RechargeTier7_BatchCount, Target.RechargeTier7_TotalAmount = Source.RechargeTier7_TotalAmount,
            Target.RechargeTier8_BatchCount = Source.RechargeTier8_BatchCount, Target.RechargeTier8_TotalAmount = Source.RechargeTier8_TotalAmount,
            
            Target.UpdateTime = GETDATE(),
            Target.ShopID = Source.ShopID

    WHEN NOT MATCHED BY TARGET THEN
        INSERT (
            DateSK, ShopSK, ShopID, DayTimeRevenue, NightTimeRevenue, TotalRevenue, TotalBatches, BuffetGuestCount, TotalDirectFallGuests, DayTimeBatchCount, NightTimeBatchCount, NightTimeGuestCount, AvgPricePerBill_Buffet, AvgPricePerGuest_Buffet, AvgPricePerGuest_PrimeTime,
            ComplimentaryBatches, ComplimentaryRevenue,
            PrivilegeBooking_Count_0Yuan, PrivilegeBooking_Count_5Yuan, PrivilegeBooking_Count_10Yuan, PrivilegeBooking_Count_15Yuan, Revenue_PrivilegeBooking,
            Night_FreeMeal_Batches, Night_FreeMeal_Revenue, Night_Buyout_Batches, Night_DrinkPackage_Batches, Night_FreeConsumption_Batches, Night_RoomFee_Batches, Night_Other_Batches,
            Revenue_OfficialAccount, Revenue_Meituan, Revenue_Douyin, Revenue_Bank, Revenue_Meituan_Booking, Revenue_Meituan_GroupBuy, Revenue_Douyin_Booking, Revenue_Douyin_GroupBuy, Revenue_Bank_GF, Revenue_Bank_CITIC, Revenue_Bank_UnionPay,
            Fee_Meituan_Booking, Fee_Meituan_GroupBuy, Fee_Douyin_Booking, Fee_Douyin_GroupBuy, Fee_Bank_GF, Fee_Bank_CITIC, Fee_Bank_UnionPay, Fee_Bank_Total,
            DailyRecharge_Total, DailyRecharge_Meituan, DailyRecharge_Meituan_Ratio,
            RechargeTier1_BatchCount, RechargeTier1_TotalAmount, RechargeTier2_BatchCount, RechargeTier2_TotalAmount, RechargeTier3_BatchCount, RechargeTier3_TotalAmount, RechargeTier4_BatchCount, RechargeTier4_TotalAmount, RechargeTier5_BatchCount, RechargeTier5_TotalAmount, RechargeTier6_BatchCount, RechargeTier6_TotalAmount, RechargeTier7_BatchCount, RechargeTier7_TotalAmount, RechargeTier8_BatchCount, RechargeTier8_TotalAmount,
            CreateTime, UpdateTime
        )
        VALUES (
            Source.DateSK, Source.ShopSK, Source.ShopID, Source.DayTimeRevenue, Source.NightTimeRevenue, Source.TotalRevenue, Source.TotalBatches, Source.BuffetGuestCount, Source.TotalDirectFallGuests, Source.DayTimeBatchCount, Source.NightTimeBatchCount, Source.NightTimeGuestCount, Source.AvgPricePerBill_Buffet, Source.AvgPricePerGuest_Buffet, Source.AvgPricePerGuest_PrimeTime,
            Source.ComplimentaryBatches, Source.ComplimentaryRevenue,
            Source.PrivilegeBooking_Count_0Yuan, Source.PrivilegeBooking_Count_5Yuan, Source.PrivilegeBooking_Count_10Yuan, Source.PrivilegeBooking_Count_15Yuan, Source.Revenue_PrivilegeBooking,
            Source.Night_FreeMeal_Batches, Source.Night_FreeMeal_Revenue, Source.Night_Buyout_Batches, Source.Night_DrinkPackage_Batches, Source.Night_FreeConsumption_Batches, Source.Night_RoomFee_Batches, Source.Night_Other_Batches,
            Source.Revenue_OfficialAccount, Source.Revenue_Meituan, Source.Revenue_Douyin, Source.Revenue_Bank, Source.Revenue_Meituan_Booking, Source.Revenue_Meituan_GroupBuy, Source.Revenue_Douyin_Booking, Source.Revenue_Douyin_GroupBuy, Source.Revenue_Bank_GF, Source.Revenue_Bank_CITIC, Source.Revenue_Bank_UnionPay,
            Source.Fee_Meituan_Booking, Source.Fee_Meituan_GroupBuy, Source.Fee_Douyin_Booking, Source.Fee_Douyin_GroupBuy, Source.Fee_Bank_GF, Source.Fee_Bank_CITIC, Source.Fee_Bank_UnionPay, Source.Fee_Bank_Total,
            Source.DailyRecharge_Total, Source.DailyRecharge_Meituan, Source.DailyRecharge_Meituan_Ratio,
            Source.RechargeTier1_BatchCount, Source.RechargeTier1_TotalAmount, Source.RechargeTier2_BatchCount, Source.RechargeTier2_TotalAmount, Source.RechargeTier3_BatchCount, Source.RechargeTier3_TotalAmount, Source.RechargeTier4_BatchCount, Source.RechargeTier4_TotalAmount, Source.RechargeTier5_BatchCount, Source.RechargeTier5_TotalAmount, Source.RechargeTier6_BatchCount, Source.RechargeTier6_TotalAmount, Source.RechargeTier7_BatchCount, Source.RechargeTier7_TotalAmount, Source.RechargeTier8_BatchCount, Source.RechargeTier8_TotalAmount,
            GETDATE(), GETDATE()
        );

    PRINT N'成功: Fact_Daily_Shop_Summary 表已通过暂存表和MERGE操作更新/插入。';

    SET NOCOUNT OFF;
    RETURN 0;
END
