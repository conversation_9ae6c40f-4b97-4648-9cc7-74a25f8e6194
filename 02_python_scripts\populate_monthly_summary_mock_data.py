import pyodbc
import random

# Connection details
conn_str = (
    'DRIVER={ODBC Driver 17 for SQL Server};'
    'SERVER=192.168.2.5;'
    'DATABASE=operatedata;'
    'UID=sa;'
    'PWD=Musicbox123;'
    'TrustServerCertificate=yes;'
)

# The month to generate data for
YEAR = 2025
MONTH = 7

def generate_mock_data(shop_id):
    """Generates a dictionary of mock data for one shop for one month."""
    data = {
        'Year': YEAR,
        'Month': MONTH,
        'ShopID': shop_id
    }

    # Base Revenue
    rev_total = random.uniform(500000, 2000000)
    data['Revenue_Total'] = round(rev_total, 2)
    data['Revenue_Total_YoY'] = random.uniform(-0.2, 0.3)
    data['Revenue_Total_MoM'] = random.uniform(-0.1, 0.15)

    # Offline Revenue is a large portion of total
    data['Revenue_Offline'] = round(rev_total * random.uniform(0.9, 0.98), 2)
    data['Revenue_Offline_YoY'] = random.uniform(-0.2, 0.3)
    data['Revenue_Offline_MoM'] = random.uniform(-0.1, 0.15)

    # Major Components (must sum up to total)
    r_privilege = rev_total * 0.05
    r_official = rev_total * 0.10
    r_meituan = rev_total * 0.30
    r_douyin = rev_total * 0.40
    r_bank = rev_total * 0.10
    r_other = rev_total - (r_privilege + r_official + r_meituan + r_douyin + r_bank)

    data['Revenue_PrivilegeBooking'] = round(r_privilege, 2)
    data['Revenue_OfficialAccount'] = round(r_official, 2)
    data['Revenue_Meituan'] = round(r_meituan, 2)
    data['Revenue_Douyin'] = round(r_douyin, 2)
    data['Revenue_Bank'] = round(r_bank, 2)
    data['Revenue_OtherBusiness'] = round(r_other, 2)

    # Minor Components (must sum up to their parent major component)
    data['Revenue_Douyin_Booking'] = round(r_douyin * 0.4, 2)
    data['Revenue_Douyin_GroupBuy'] = round(r_douyin * 0.6, 2)
    data['Revenue_Meituan_Booking'] = round(r_meituan * 0.3, 2)
    data['Revenue_Meituan_GroupBuy'] = round(r_meituan * 0.7, 2)
    data['Revenue_Bank_GF'] = round(r_bank * 0.5, 2)
    data['Revenue_Bank_CITIC'] = round(r_bank * 0.3, 2)
    data['Revenue_Bank_UnionPay'] = round(r_bank * 0.2, 2)

    # Fees (as a percentage of their component revenue)
    data['Fee_Douyin_Booking'] = round(data['Revenue_Douyin_Booking'] * 0.03, 2)
    data['Fee_Douyin_GroupBuy'] = round(data['Revenue_Douyin_GroupBuy'] * 0.03, 2)
    data['Fee_Meituan_Booking'] = round(data['Revenue_Meituan_Booking'] * 0.02, 2)
    data['Fee_Meituan_GroupBuy'] = round(data['Revenue_Meituan_GroupBuy'] * 0.02, 2)
    data['Fee_Bank_Total'] = round(r_bank * 0.006, 2)

    # Counts
    for i in [0, 5, 10, 15]:
        data[f'Count_PrivilegeBooking_{i}Yuan'] = random.randint(10, 100)

    # Set YoY/MoM for other fields to None for simplicity
    for key in list(data.keys()):
        if ('_YoY' in key or '_MoM' in key) and key not in ['Revenue_Total_YoY', 'Revenue_Total_MoM', 'Revenue_Offline_YoY', 'Revenue_Offline_MoM']:
            data[key] = None # Not mocking all growth fields

    return data

try:
    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()

    # 1. Get active shops
    cursor.execute("SELECT ShopID FROM Dim_Shop WHERE IsActive = 1;")
    active_shops = [row.ShopID for row in cursor.fetchall()]

    if not active_shops:
        print("错误：在 Dim_Shop 中没有找到任何 active (IsActive=1) 的门店。")
    else:
        # 2. Delete existing data for the target month
        print(f"正在删除 {YEAR}-{MONTH} 的旧数据...")
        cursor.execute("DELETE FROM Fact_Monthly_Shop_Summary WHERE Year = ? AND Month = ?", (YEAR, MONTH))
        
        print(f"将为 {len(active_shops)} 家门店生成 {YEAR}-{MONTH} 的模拟数据...")
        
        # 3. Loop through shops and insert mock data
        all_mock_data = [generate_mock_data(shop_id) for shop_id in active_shops]
        
        # Construct a single large INSERT statement for efficiency
        # Get columns from the first dictionary, assuming all are the same
        columns = all_mock_data[0].keys()
        sql_insert = f"""
            INSERT INTO Fact_Monthly_Shop_Summary ({ ', '.join([f'[{col}]' for col in columns]) }) 
            VALUES ({ ', '.join('[' * len(columns)) });
        """
        # The above line has a bug, should be VALUES (?, ?, ...)
        # Correcting it:
        sql_insert = f"""
            INSERT INTO Fact_Monthly_Shop_Summary ({ ', '.join([f'[{col}]' for col in columns]) }) 
            VALUES ({ ', '.join('?' * len(columns)) });
        """

        # Create a list of tuples for executemany
        params_list = [tuple(row[col] for col in columns) for row in all_mock_data]

        cursor.executemany(sql_insert, params_list)
        cnxn.commit()
        print(f"成功为 {len(active_shops)} 家门店插入了模拟数据。")

except Exception as e:
    print(f"执行出错: {e}")
    try:
        cnxn.rollback()
    except Exception as roll_e:
        pass # Ignore rollback errors
finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
