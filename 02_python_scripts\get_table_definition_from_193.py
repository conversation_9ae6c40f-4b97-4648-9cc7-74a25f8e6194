
import pyodbc
import os

# Connection details for the store server (193)
SERVER = os.getenv('DB_HOST_193', '*************')
DATABASE = os.getenv('DB_NAME_193', 'dbfood')
USERNAME = os.getenv('DB_USER_193', 'sa')
PASSWORD = os.getenv('DB_PASS_193', 'Musicbox@123')
TABLE_NAME = 'RoomStatisticsHourly'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD};TrustServerCertificate=yes;'

print(f"Connecting to {SERVER}/{DATABASE} to get definition for table: {TABLE_NAME}")

cnxn = None
try:
    cnxn = pyodbc.connect(conn_str, timeout=5)
    cursor = cnxn.cursor()
    print("Connection successful.")

    # --- 1. Get Column Info ---
    print(f"\n--- Columns for {TABLE_NAME} ---")
    col_sql = """
    SELECT 
        c.name AS ColumnName, 
        t.name AS DataType, 
        c.max_length AS MaxLength, 
        c.precision AS Precision, 
        c.scale AS Scale, 
        c.is_nullable AS IsNullable
    FROM sys.columns c
    JOIN sys.types t ON c.user_type_id = t.user_type_id
    WHERE c.object_id = OBJECT_ID(?)
    """
    cursor.execute(col_sql, f'dbo.{TABLE_NAME}')
    for row in cursor.fetchall():
        print(f"- {row.ColumnName} ({row.DataType}, Nullable: {row.IsNullable})")

    # --- 2. Get Primary Key ---
    print(f"\n--- Primary Key for {TABLE_NAME} ---")
    pk_sql = """
    SELECT c.name AS ColumnName
    FROM sys.indexes i
    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
    WHERE i.is_primary_key = 1 AND i.object_id = OBJECT_ID(?)
    ORDER BY ic.key_ordinal;
    """
    cursor.execute(pk_sql, f'dbo.{TABLE_NAME}')
    pk_cols = [row.ColumnName for row in cursor.fetchall()]
    if pk_cols:
        print(f"Primary Key columns: {', '.join(pk_cols)}")
    else:
        print("No primary key found.")

    # --- 3. Get Default Constraints ---
    print(f"\n--- Default Constraints for {TABLE_NAME} ---")
    dc_sql = """
    SELECT 
        c.name AS ColumnName, 
        dc.name AS ConstraintName, 
        dc.definition AS DefaultValue
    FROM sys.default_constraints dc
    JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
    WHERE dc.parent_object_id = OBJECT_ID(?)
    """
    cursor.execute(dc_sql, f'dbo.{TABLE_NAME}')
    defaults = cursor.fetchall()
    if defaults:
        for row in defaults:
            print(f"- Column '{row.ColumnName}' defaults to: {row.DefaultValue}")
    else:
        print("No default constraints found.")

    # --- 4. Get Triggers ---
    print(f"\n--- Triggers on {TABLE_NAME} ---")
    tr_sql = """
    SELECT 
        name AS TriggerName, 
        OBJECT_DEFINITION(object_id) AS TriggerDefinition
    FROM sys.triggers
    WHERE parent_id = OBJECT_ID(?)
    """
    cursor.execute(tr_sql, f'dbo.{TABLE_NAME}')
    triggers = cursor.fetchall()
    if triggers:
        for row in triggers:
            print(f"- Trigger '{row.TriggerName}':\n{row.TriggerDefinition}")
    else:
        print("No triggers found.")

except pyodbc.Error as ex:
    print(f"A database error occurred: {ex}")
except Exception as ex:
    print(f"An unexpected error occurred: {ex}")

finally:
    if cnxn:
        cnxn.close()
        print("\nConnection closed.")
