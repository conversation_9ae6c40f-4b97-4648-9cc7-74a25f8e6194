'''
This script connects to the operatedata database on the *********** server 
and retrieves the definition of the stored procedure usp_Sync_RMS_DailyOpenData.
'''
import pyodbc
import pandas as pd

# Connection details from memory
server = '***********'
database = 'operatedata'
username = 'sa'
password = 'Musicbox123'

# Connection string
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;'

# Stored procedure to analyze
sp_name = 'usp_Sync_RMS_DailyOpenData'

sql = f"sp_helptext '{sp_name}'"

try:
    # Connect to the database
    cnxn = pyodbc.connect(conn_str)
    print(f"Successfully connected to {server}/{database}")

    # Execute the query
    df = pd.read_sql(sql, cnxn)

    print(f"\n--- Definition of {sp_name} ---\n")
    # Print the definition line by line
    for index, row in df.iterrows():
        print(row['Text'])

    print(f"\n--- End of Definition ---\n")

except pyodbc.Error as ex:
    sqlstate = ex.args[0]
    print(f"Database connection or query failed.")
    print(f"SQLSTATE: {sqlstate}")
    print(ex)

finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("Connection closed.")
