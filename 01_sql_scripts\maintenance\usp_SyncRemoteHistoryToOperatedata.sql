-- Stored Procedure: usp_SyncRemoteHistoryToOperatedata (v2 - Bugfix)
-- Description: Syncs data in batches from remote openhistory and openhistory_bak tables
--              to the local operatedata.dbo.opencacheinfo table.

IF OBJECT_ID('dbo.usp_SyncRemoteHistoryToOperatedata', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_SyncRemoteHistoryToOperatedata;
    PRINT 'Procedure usp_SyncRemoteHistoryToOperatedata dropped.';
END
GO

CREATE PROCEDURE dbo.usp_SyncRemoteHistoryToOperatedata
AS
BEGIN
    SET NOCOUNT ON;

    -- Configuration
    DECLARE @BatchSize INT = 50000;
    PRINT 'Batch size set to ' + CAST(@BatchSize AS VARCHAR(20));

    -- Temp table to hold keys of new records
    IF OBJECT_ID('tempdb..#SourceKeys') IS NOT NULL
    BEGIN
        DROP TABLE #SourceKeys;
    END
    CREATE TABLE #SourceKeys (
        RowID INT IDENTITY(1,1) PRIMARY KEY,
        Ikey VARCHAR(36) NOT NULL UNIQUE
    );

    -- 1. Identify all new records and store their keys in the temp table
    PRINT 'Identifying new records to sync...';
    INSERT INTO #SourceKeys (Ikey)
    SELECT Ikey FROM (
        SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory
        UNION ALL
        SELECT Ikey FROM cloudRms2019.rms2019.dbo.openhistory_bak
    ) AS SourceData
    WHERE NOT EXISTS (
        SELECT 1
        FROM operatedata.dbo.opencacheinfo dest WITH (NOLOCK)
        WHERE dest.Ikey = SourceData.Ikey
    )
    GROUP BY Ikey; -- Add GROUP BY to ensure keys are unique if sources have duplicates

    DECLARE @TotalRows INT = @@ROWCOUNT;
    DECLARE @CurrentRow INT = 1;
    DECLARE @BatchesProcessed INT = 0;
    
    IF @TotalRows = 0
    BEGIN
        PRINT 'No new records to sync. Exiting.';
        SET NOCOUNT OFF;
        RETURN;
    END

    DECLARE @TotalBatches INT = CEILING(CAST(@TotalRows AS FLOAT) / @BatchSize);

    PRINT CAST(@TotalRows AS VARCHAR(20)) + ' new records found. Starting sync in ' + CAST(@TotalBatches AS VARCHAR(20)) + ' batches.';
    PRINT '--------------------------------------------------';

    -- 2. Loop through the batches and insert data
    WHILE @CurrentRow <= @TotalRows
    BEGIN
        SET @BatchesProcessed = @BatchesProcessed + 1;
        PRINT 'Processing batch ' + CAST(@BatchesProcessed AS VARCHAR(20)) + ' of ' + CAST(@TotalBatches AS VARCHAR(20)) + '...';

        -- Combine sources and insert the current batch
        INSERT INTO operatedata.dbo.opencacheinfo (
            Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime,
            Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName, CtNo, CtName,
            PtNo, PtName, BookMemory, BookStatus, CheckinStatus, BookShopId, BookUserId,
            BookUserName, BookDateTime, Invno, Openmemory, OrderUserID, OrderUserName,
            RmNo, Val1, FromRmNo, IsBirthday, Remark
        )
        SELECT
            src.Ikey, src.BookNo, src.ShopId, src.CustKey, src.CustName, src.CustTel, src.ComeDate, src.ComeTime,
            src.Beg_Key, src.Beg_Name, src.End_Key, src.End_Name, src.Numbers, src.RtNo, src.RtName, src.CtNo, src.CtName,
            src.PtNo, src.PtName, src.BookMemory, src.BookStatus, src.CheckinStatus, src.BookShopId, src.BookUserId,
            src.BookUserName, src.BookDateTime, src.Invno, src.Openmemory, src.OrderUserID, src.OrderUserName,
            src.RmNo, src.Val1, src.FromRmNo, src.IsBirthday, src.Remark
        FROM (
            -- CORRECTED UNION ALL BLOCK
            SELECT * FROM cloudRms2019.rms2019.dbo.openhistory
            UNION ALL
            SELECT *, NULL AS Remark FROM cloudRms2019.rms2019.dbo.openhistory_bak
        ) AS src
        INNER JOIN #SourceKeys sk ON src.Ikey = sk.Ikey
        WHERE sk.RowID BETWEEN @CurrentRow AND (@CurrentRow + @BatchSize - 1);

        PRINT CAST(@@ROWCOUNT AS VARCHAR(20)) + ' rows inserted.';
        PRINT '--------------------------------------------------';

        -- Move to the next batch
        SET @CurrentRow = @CurrentRow + @BatchSize;

        -- Small delay to yield CPU, crucial in a loop
        WAITFOR DELAY '00:00:01';
    END

    -- Clean up
    DROP TABLE #SourceKeys;
    PRINT 'Sync completed successfully!';
    SET NOCOUNT OFF;
END
GO