# This script finds all missing months from the earliest record up to September 2025.
# Version: 1

import pyodbc
from collections import defaultdict

# Connection details
CONN_STR = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'

# SQL query to get all distinct YYYYMM combinations
SQL = "SELECT DISTINCT SUBSTRING(ComeDate, 1, 6) AS YearMonth FROM cloudRms2019.rms2019.dbo.openhistory_bak WHERE LEN(ComeDate) = 8 AND ISNUMERIC(ComeDate) = 1;"

def main():
    print("Connecting to the database...")
    try:
        conn = pyodbc.connect(CONN_STR)
        cursor = conn.cursor()
        
        print("Executing query to get all existing year-month combinations...")
        cursor.execute(SQL)
        
        rows = cursor.fetchall()
        existing_year_months = {row.YearMonth for row in rows if row.YearMonth}

        if not existing_year_months:
            print("No valid data found in the table. Cannot determine date range.")
            return

        # Determine the date range from the data
        min_year = int(min(existing_year_months)[:4])
        # The upper bound is fixed by the user request
        end_year = 2025
        end_month_for_last_year = 9

        print(f"Analyzing data from {min_year} to {end_year}-{end_month_for_last_year:02d}...")

        # Generate the set of all months that should exist
        complete_year_months = set()
        for year in range(min_year, end_year + 1):
            # Determine the end month for the current year in the loop
            last_month = 12
            if year == end_year:
                last_month = end_month_for_last_year
            
            for month in range(1, last_month + 1):
                complete_year_months.add(f"{year}{month:02d}")

        # Find the difference
        missing_year_months = sorted(list(complete_year_months - existing_year_months))
        
        print("-"*70)
        if not missing_year_months:
            print("Result: No months are missing in the specified range.")
        else:
            print("Result: Found the following missing year-month combinations:")
            # Group by year for readability
            missing_by_year = defaultdict(list)
            for ym in missing_year_months:
                year = ym[:4]
                month = ym[4:]
                missing_by_year[year].append(month)
            
            for year in sorted(missing_by_year.keys()):
                print(f"  - Year {year}: {', '.join(missing_by_year[year])}")
        print("-"*70)

    except pyodbc.Error as ex:
        print(f"Database query failed: {ex}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("\nConnection closed.")

if __name__ == "__main__":
    main()
