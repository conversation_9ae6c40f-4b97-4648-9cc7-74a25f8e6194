import pyodbc

# Database connection details
conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=***********;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
sql_file_path = r'C:\Users\<USER>\CascadeProjects\KTV_Data_Analysis\01_sql_scripts\tests_and_adhoc\get_invoice_details.sql'

cnxn = None
cursor = None
try:
    with open(sql_file_path, 'r', encoding='utf-8-sig') as f:
        sql_query = f.read()

    cnxn = pyodbc.connect(conn_str)
    cursor = cnxn.cursor()
    
    cursor.execute(sql_query)
    
    rows = cursor.fetchall()
    columns = [column[0] for column in cursor.description]

    # Print header
    header = f'{columns[0]:<15} | {columns[1]:<10} | {columns[2]:<12} | {columns[3]:<25} | {columns[4]}'
    print(header)
    print('-' * (len(header) + 5))

    # Print rows
    if rows:
        for row in rows:
            formatted_row = f'{str(row[0]):<15} | {str(row[1]):<10} | {str(row[2]):<12} | {str(row[3]):<25} | {str(row[4])}'
            print(formatted_row)
    else:
        print("No details found for this invoice.")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    if cursor:
        cursor.close()
    if cnxn:
        cnxn.close()
