'''
Populates the new Dim_Deal_Map table, correctly handling the composite key (ShopId, FdNo)
and including the FdPrice2 from the food table. This version includes all items regardless of fdlack status.
'''
import pyodbc
import datetime

def get_channel_mapping(cursor):
    '''Fetch the ChannelSK mapping from Dim_Channel into a dictionary.'''
    print("正在从 Dim_Channel 获取渠道ID...")
    channel_map = {}
    cursor.execute("SELECT ChannelSK, ChannelName, SubChannelName FROM Dim_Channel")
    for row in cursor.fetchall():
        channel_map[row.ChannelName] = row.ChannelSK
        channel_map[row.SubChannelName] = row.ChannelSK
    print(f"渠道ID获取成功: {channel_map}")
    return channel_map

def populate_new_deal_map():
    '''Main function to populate the deal map.'''
    conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=192.168.2.5;DATABASE=operatedata;UID=sa;PWD=Musicbox123;TrustServerCertificate=yes;'
    
    conn = None
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        channel_map = get_channel_mapping(cursor)
        
        # Step 1: Select all food items, now including FdPrice2 and ignoring fdlack status
        print("正在从 food 表查询所有商品(无论是否启用)，包含价格信息...")
        cursor.execute("SELECT ShopId, FdNo, FdCName, FdPrice2 FROM food")
        food_items = cursor.fetchall()
        print(f"查询到 {len(food_items)} 条商品记录。")

        # Step 2: Process and determine the channel for each item in Python
        records_to_insert = []
        now = datetime.datetime.now()

        for item in food_items:
            shop_id, fd_no, fd_cname, fd_price2 = item.ShopId, item.FdNo, item.FdCName, item.FdPrice2
            channel_sk = None

            # Determine channel based on name
            if '美团' in fd_cname or '美预' in fd_cname:
                channel_sk = channel_map.get('美团')
            elif '抖音' in fd_cname or '抖预' in fd_cname:
                channel_sk = channel_map.get('抖音')
            elif '广发' in fd_cname or '中信' in fd_cname or '广日' in fd_cname:
                channel_sk = channel_map.get('银行')

            if channel_sk:
                records_to_insert.append((shop_id, fd_no, channel_sk, fd_cname, fd_price2, now, now))

        if not records_to_insert:
            print("没有找到符合分类条件的商品可供插入。")
            return

        print(f"找到 {len(records_to_insert)} 条可映射的商品，准备插入 Dim_Deal_Map...")

        # Step 3: Clear the table and bulk insert
        print("清空旧的映射数据...")
        cursor.execute("TRUNCATE TABLE Dim_Deal_Map")
        
        insert_sql = """
        INSERT INTO Dim_Deal_Map (ShopId, FdNo, ChannelSK, Source_FdName, FdPrice2, CreateTime, UpdateTime)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.executemany(insert_sql, records_to_insert)
        conn.commit()
        print(f"成功插入 {len(records_to_insert)} 条新记录到 Dim_Deal_Map。")

    except pyodbc.Error as ex:
        print(f"数据库操作失败: {ex}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    populate_new_deal_map()
