
import openpyxl
import csv
import re
import os

# --- Configuration ---
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
input_excel_path = os.path.join(project_root, '重复歌曲数量 .xlsx')
output_csv_path = os.path.join(project_root, '03_reports', 'duplicate_songs_report_cleaned_final.csv')

# Regex to find potential song numbers
SONG_ID_REGEX = r"['a-zA-Z0-9]{6,}"


def find_representative_song_id(row_data):
    # row_data is a list of cell values
    if len(row_data) > 8:
        # Columns are: 备注 (6), 相同 (7), 不相同 (8)
        search_string = ' '.join(str(s) for s in row_data[6:9] if s is not None)
        matches = re.findall(SONG_ID_REGEX, search_string)
        return matches[0] if matches else ''
    return ''

# --- Main Execution ---

processed_rows = []

try:
    # 1. Read from Excel
    workbook = openpyxl.load_workbook(input_excel_path, data_only=True)
    sheet = workbook.worksheets[0] # Get the first sheet

    # 2. Prepare to write to a proper CSV
    with open(output_csv_path, 'w', newline='', encoding='utf-8-sig') as f_out:
        writer = csv.writer(f_out)

        # 3. Process Header
        header_row = [cell.value for cell in next(sheet.iter_rows())]
        header_row.append('统一歌号')
        writer.writerow(header_row)
        
        # 4. Process Data Rows
        song_count = 0
        for row in sheet.iter_rows(min_row=2): # Start from the second row
            if all(cell.value is None for cell in row): # Skip fully empty rows
                continue

            song_count += 1
            cell_values = [cell.value for cell in row]
            
            song_id = find_representative_song_id(cell_values)
            cell_values.append(song_id)
            
            writer.writerow(cell_values)

    print(f"处理完成，共处理 {song_count} 首歌曲。已将结果保存到: {os.path.abspath(output_csv_path)}")

except Exception as e:
    print(f"处理时发生错误: {e}")

