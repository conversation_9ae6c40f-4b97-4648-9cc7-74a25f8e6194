DECLARE @InvNo VARCHAR(50) = 'A03219230';

-- Query 1: Get the final guest count from rmcloseinfo
SELECT
    'rmcloseinfo' AS SourceTable,
    InvNo,
    Numbers AS GuestCount,
    'Final Settled Count' AS ItemName,
    CloseDatetime AS RecordTime
FROM
    operatedata.dbo.rmcloseinfo
WHERE
    InvNo = @InvNo

UNION ALL

-- Query 2: Get the detailed guest count items from fdcashbak
SELECT
    'fdcashbak' AS SourceTable,
    InvNo,
    FdQty AS GuestCount,
    FdCName AS ItemName,
    BillTime AS RecordTime
FROM
    operatedata.dbo.fdcashbak
WHERE
    InvNo = @InvNo
    AND FdCName LIKE N'%消费人数%'
    AND CashType = 'N'
ORDER BY
    RecordTime;
