import pyodbc

# Connection details from memory
conn_str = (
    'DRIVER={ODBC Driver 17 for SQL Server};'
    'SERVER=***********;'
    'DATABASE=operatedata;'
    'UID=sa;'
    'PWD=Musicbox123;'
    'TrustServerCertificate=yes;'
)

# SQL script with explicit transaction control and NVARCHAR literals (N'...')
sql_script = """
BEGIN TRANSACTION;

-- Step 1: Insert Bank Deals
INSERT INTO Dim_Deal_Item_Mapping (Source_FdName, Channel, SubChannel, IsFeeApplicable, FeeRate)
SELECT DISTINCT
    d.DealName,
    N'银行',
    b.BankName,
    1, -- Assuming bank deals have fees, can be adjusted
    0.006 -- Assuming a default 0.6% fee for banks, can be adjusted
FROM 
    Dim_Bank_Deal d
JOIN 
    Dim_Bank b ON d.BankSK = b.BankSK
WHERE 
    NOT EXISTS (SELECT 1 FROM Dim_Deal_Item_Mapping m WHERE m.Source_FdName = d.DealName);

-- Step 2: Inser<PERSON> Meituan and <PERSON><PERSON><PERSON> Deals
INSERT INTO Dim_Deal_Item_Mapping (Source_FdName, Channel, SubChannel, IsFeeApplicable, FeeRate)
SELECT DISTINCT
    FdCName,
    CASE 
        WHEN FdCName LIKE N'%美团%' OR FdCName LIKE N'%美预%' THEN N'美团'
        WHEN FdCName LIKE N'%抖音%' OR FdCName LIKE N'%抖预%' THEN N'抖音'
    END,
    CASE 
        WHEN FdCName LIKE N'%美预%' OR FdCName LIKE N'%抖预%' THEN N'预约'
        ELSE N'团购'
    END,
    1, -- Assuming these deals have fees
    0.03 -- Assuming a default 3% fee, can be adjusted
FROM
    food
WHERE
    (FdCName LIKE N'%美团%' OR FdCName LIKE N'%美预%' OR FdCName LIKE N'%抖音%' OR FdCName LIKE N'%抖预%')
    AND NOT EXISTS (SELECT 1 FROM Dim_Deal_Item_Mapping m WHERE m.Source_FdName = food.FdCName);

COMMIT TRANSACTION;
"""

try:
    cnxn = pyodbc.connect(conn_str, autocommit=False) # autocommit=False to control transaction manually
    cursor = cnxn.cursor()
    print("数据库连接成功，正在执行填充脚本...")
    cursor.execute(sql_script)
    # The commit is in the SQL script itself, but a connection-level commit is good practice.
    # cnxn.commit() # This is redundant if COMMIT TRANSACTION is in the script, but safer.
    print("脚本执行成功，数据已提交。")
except Exception as e:
    print(f"执行出错: {e}")
    try:
        cnxn.rollback()
        print("操作已回滚。")
    except Exception as roll_e:
        print(f"回滚失败: {roll_e}")
finally:
    if 'cnxn' in locals() and cnxn:
        cnxn.close()
        print("数据库连接已关闭。")
